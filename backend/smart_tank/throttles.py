# from rest_framework.throttling import SimpleRateThrottle
# from .models import ThirdPartyAPIKey

# class APIKeyRateThrottle(SimpleRateThrottle):
#     scope = 'third_party'

#     def get_cache_key(self, request, view):
#         api_key = request.headers.get("X-API-KEY")
#         if not api_key:
#             return None

#         try:
#             key_obj = ThirdPartyAPIKey.objects.get(key=api_key, is_active=True)
#             request.third_party_key = key_obj
#         except ThirdPartyAPIKey.DoesNotExist:
#             return None

#         return self.cache_format % {
#             'scope': self.scope,
#             'ident': api_key
#         }

#     def get_rate(self):
#         request = getattr(self, 'request', None)
#         key_obj = getattr(request, 'third_party_key', None)
#         return key_obj.rate_limit if key_obj else super().get_rate()
