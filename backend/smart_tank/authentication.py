# from rest_framework.authentication import BaseAuthentication
# from rest_framework.exceptions import AuthenticationFailed
# from .models import ThirdPartyAPIKey

# class APIKeyAuthentication(BaseAuthentication):
#     def authenticate(self, request):
#         api_key = request.headers.get("X-API-KEY")
#         if not api_key:
#             return None
#         try:
#             key_obj = ThirdPartyAPIKey.objects.get(key=api_key, is_active=True)
#         except ThirdPartyAPIKey.DoesNotExist:
#             raise AuthenticationFailed("Invalid or inactive API key")

#         request.third_party_key = key_obj
#         return (None, None)
