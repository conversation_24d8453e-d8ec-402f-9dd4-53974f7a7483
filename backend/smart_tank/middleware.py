from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse


class ThirdPartyMiddleware(MiddlewareMixin):
    """
    Middleware to check if the user is a third-party user.
    If not, it returns a 403 Forbidden response.
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        # Define the endpoint you want to monitor
        self.target_endpoint = '/api/v1/third-party/'  # Change this to your desired endpoint
    
    def process_request(self, request):
        # Check if the request path starts with our target endpoint
        if request.path.startswith(self.target_endpoint):
            # Check if user is authenticated
            if hasattr(request, 'user') and request.user.is_authenticated:
                if not request.user.third_party:
                    # If the user is not a third-party user, return a 403 Forbidden response
                    return JsonResponse({'data': [], 'error': 'This endpoint is only for third-party users', 'code': 403, 'status': 'failed'}, status=403)
                print(f"User '{request.user}' accessed {request.path}")

            else:
                print(f"Anonymous user accessed {request.path}")
        # If the request does not match the target endpoint or the user is a third-party user,
        # we simply return None to continue processing the request.
        # Continue processing the request
        return None