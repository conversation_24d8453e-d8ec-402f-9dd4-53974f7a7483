import secrets
from django.db import models

class ThirdPartyAP<PERSON><PERSON>ey(models.Model):
    name = models.CharField(max_length=100)
    key = models.CharField(max_length=100, unique=True, editable=False)
    is_active = models.BooleanField(default=True)
    rate_limit = models.Char<PERSON>ield(max_length=20, default="10/minute")
    created_at = models.DateTimeField(auto_now_add=True)

    def save(self, *args, **kwargs):
        if not self.key:
            self.key = secrets.token_urlsafe(32)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({'active' if self.is_active else 'inactive'})"
