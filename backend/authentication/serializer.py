from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import serializers
 
from ..users.serializer import UserSerializer

#import models
from ..models import PasswordReset, User, Permission


class LoginSerializer(TokenObtainPairSerializer):
    '''
    DRF simple-jwt does not update user last login after getting a new token.
    Fix is to subclass the TokenObtainPairSerializer and add the user_id to the
    serializer response data
    '''        
    def validate(self, attrs):
        data = super(LoginSerializer, self).validate(attrs)
        data.update({'user': UserSerializer(self.user).data})
        data['token'] = data['access']
        data['refresh'] = data['refresh']
        del data['access']
        return data
    
class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id','codename', 'name', 'content_type_id']


class LoginResponseSerializer(serializers.Serializer):
    user = UserSerializer()
    token = serializers.CharField()
    refresh = serializers.CharField()
    group_permission = PermissionSerializer(many=True)
class PasswordChangeSerializer(serializers.Serializer):
    '''
        PasswordChangeSerializer to enable serializer on drf-spectacular
    '''
    user_id = serializers.IntegerField(required=True)
    token = serializers.CharField(required=True)
    password = serializers.CharField(required=True)

class PasswordChangeResponseSerializer(serializers.Serializer):
    '''
        PasswordChangeSerializer to enable serializer on drf-spectacular
    '''
    user_id = serializers.CharField(required=True)
    token = serializers.CharField(required=True)
    password = serializers.CharField(required=True)


class PasswordResetRequestSerializer(serializers.Serializer):
    Email = serializers.CharField(required=True)

class EmailChangeRequestSerializer(serializers.ModelSerializer):

    Email = serializers.CharField(required=True)

    class Meta:
        model = User
        fields = [
            'id',
            'Email',
        ]

    def validate(self, args):
        email = args.get('Email', None)
        if User.objects.filter(Email=email).exists():
            raise serializers.ValidationError({'Email':('Email Already Exist')})
        return super().validate(args)


class PasswordResetConfirmSerializer(serializers.Serializer):
    """
        PasswordResetRequestSerializer to enable serializer on drf-spectacular
    """
    uid = serializers.CharField(required=True)
    token = serializers.CharField(required=True)

class PasswordResetResponseSerializer(serializers.Serializer):
    user_id = serializers.PrimaryKeyRelatedField(
        queryset=User.objects.all(),
        source='user'
    )
    message = serializers.CharField()