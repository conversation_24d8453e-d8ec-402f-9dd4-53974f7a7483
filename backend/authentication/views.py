from datetime import datetime
from decouple import config
import smtplib
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail, EmailMessage
from django.contrib.auth import get_user_model
from rest_framework import generics
from django.template import loader
from django.shortcuts import get_object_or_404
from django.contrib.auth.signals import user_logged_in
import requests

from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.exceptions import ParseError, NotFound
from email.mime.text import MIMEText

from rest_framework_simplejwt.views import TokenObtainPairView

from drf_spectacular.utils import extend_schema, extend_schema_serializer
from drf_spectacular.types import OpenApiTypes

from backend import models
from . import serializer
from backend.authentication.utils import push_notification, send_email

from .. import utils
from ..users.serializer import UserSerializer
from ..roles.serializer import PermissionSerializer


class Login(TokenObtainPairView):
    '''
    Login logs a user into the system
    '''
    serializer_class = serializer.LoginSerializer

    @extend_schema(responses=serializer.LoginResponseSerializer)
    def post(self, request, *args, **kwargs):
        # check the request body for the keys
        
        keys = request.data.keys()
        if not 'Email' in keys or request.data['Email'] == '':
            return utils.CustomResponse.Failure(error="Email Field is required")
        elif not 'password' in keys or request.data['password'] == '':
            return utils.CustomResponse.Failure(error="password Field is required")
        # check if the user exist
        try:
            user=get_user_model().objects.get(Email=request.data['Email'])
            if user.is_active == False:
                return utils.CustomResponse.Failure('This user is inactive', status=status.HTTP_403_FORBIDDEN)
        except get_user_model().DoesNotExist:
            return utils.CustomResponse.Failure("user does not exist",status=404)
        
        try:
            response = super().post(request, *args, **kwargs)
            user_id = response.data.get('user')["id"]
            user = get_user_model().objects.get(pk=user_id)
            if user.is_active == False:
                return utils.CustomResponse.Failure('This user is inactive', status=status.HTTP_403_FORBIDDEN)
            user_logged_in.send(sender=user.__class__, request=request, user=user)
            # get the group name if it is null
            if response.data["user"]['groups'] == None:
                # set the group instance to null
                group_instance = ''
                permission_in_group = []
                response.data['user']['group_permission'] = []
            else:
                group_instance = response.data["user"]['groups']['name']
            #     # using the group name get the permissions in that group
                permission_in_group = models.Group.objects.get(name=group_instance).permissions.all()
              # clean the data to be Json Serializable
                cleaned_data = PermissionSerializer(permission_in_group,many=True).data
            #     # append the cleaned data to the response
                response.data['user']['group_permission'] = cleaned_data
            return utils.CustomResponse.Success(response.data)
        except:
            return utils.CustomResponse.Failure('Invalid Login Credentials', status=status.HTTP_401_UNAUTHORIZED)



class PasswordResetRequest(APIView):
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.PasswordResetRequestSerializer

    @extend_schema(responses=OpenApiTypes.STR)
    def post(self, request, *args, **kwargs):
        serialized = self.serializer_class(data=request.data)
        
        if serialized.is_valid():
            serialized_data = serialized.validated_data
            qs = get_user_model().objects.all()
            user_email = serialized_data['Email']
            user = get_object_or_404(qs, Email=user_email)
            if not user.is_active:
                return utils.CustomResponse.Failure('This email is inactive', status=status.HTTP_403_FORBIDDEN)
            else:
                token = default_token_generator.make_token(user)
                uid = user.pk
                # create password reset object in db
                models.PasswordReset.objects.get_or_create(user_id=user.pk, token=token)

                # Use smarteye live link
                reset_link = (config('SMARTEYE_BASE_URL')+"#/auth/resetpassword/{uid}/{token}?reset=password").format(uid=uid, token=token)
                email_template_name = 'custom_email_template.html'
                email_template_reset = 'email_reset.html'
                template_context = {
                    "username": user.get_username,
                    "reset_link": reset_link
                }
                email = loader.render_to_string(email_template_name, template_context)
                reset_email = loader.render_to_string(email_template_reset, template_context)

                # breevo
                
                
                # AWS
                try:
                    message = EmailMessage(subject="Smarteye Account Password Reset",body=reset_email,reply_to=[user_email],to=[user_email])
                    message.content_subtype = "html"
                    message.send()
                    return utils.CustomResponse.Success('Email Sent to user', status=200)
                except:
                # BREEVO OPTION TWO
                    send_email(subject="Smarteye account Password Reset", body=reset_email, sender_email=config('DEFAULT_FROM_EMAIL'), receiver_email=user_email, sender_name='Smartflowtech', reply_to=config('DEFAULT_FROM_EMAIL'))
                    return utils.CustomResponse.Success('Email Sent to user', status=200)
                else:
                    return utils.CustomResponse.Failure('Email not sent', status=status.HTTP_400_BAD_REQUEST)
        else:
            return utils.CustomResponse.Failure(error=serialized.errors, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirm(APIView):
    permission_classes = ()
    authentication_classes = ()

    @extend_schema(responses=serializer.PasswordResetResponseSerializer)
    def get(self, request, *args, **kwargs):
        qs = get_user_model().objects.all()
        uid = kwargs.get('uid')
        token = kwargs.get('token')

        user = get_object_or_404(qs, pk=uid)
        if not user.is_active:
            raise NotFound('This email is inactive', 'inactive_user')
        else:
            token_valid = default_token_generator.check_token(user, token)
            if not token_valid:
                raise ParseError('Verification token is invalid or has expired!')
            reset_object = get_object_or_404(models.PasswordReset.objects.all(), user_id=uid, token=token)
            reset_object.confirmation_status = True
            reset_object.save()

            payload = {
                'user_id': user.pk,
                'message': 'Valid verification link'
            }

            return utils.CustomResponse.Success(payload)


class PasswordChange(APIView):
    permission_classes = ()
    authentication_classes = ()
    serializer_class = serializer.PasswordChangeSerializer
    
    @extend_schema(responses=OpenApiTypes.STR)
    def post(self, request, *args, **kwargs):
        qs = get_user_model().objects.all()

        # serialized incoming data
        serialized = self.serializer_class(data=request.data)

        if serialized.is_valid():
            serialized_data = serialized.validated_data
            user_id = serialized_data['user_id']
            
            token = serialized_data['token']

            user = get_object_or_404(qs, pk=user_id)
            if not user.is_active:
                raise NotFound('This email is inactive', 'inactive_user')
            token_valid = default_token_generator.check_token(user, token)
            if not token_valid:
                raise ParseError('Verification token is invalid or has expired!')
            new_password = serialized_data['password']
            user.set_password(new_password)
            user.save()

            return utils.CustomResponse.Success('Password Changed successfully')
        else:
            return utils.CustomResponse.Failure(error=serialized.errors, status=status.HTTP_400_BAD_REQUEST)

class EmailRequestChange(APIView):

    def get_object(self, pk):
        try:
            return models.User.objects.get(pk=pk)
        except models.User.DoesNotExist:
            raise NotFound('User Does Not Exist')

    def put(self, request, pk, format=None):
        snippet = self.get_object(pk)
        serializer_data = serializer.EmailChangeRequestSerializer(snippet, data=request.data)
        if serializer_data.is_valid():
            serializer_data.save()
            user_id = serializer_data.data['id']
            user_email = serializer_data.data['Email']
            username = request.user
            email_template_name = 'email_change_template.html'
            template_context = {
                "username": username,
                "user_email": user_email
            }
            email = loader.render_to_string(email_template_name, template_context)
             # AWS
            try:
                message = EmailMessage(subject="Account Updated Successfully",body=email,reply_to=[user_email],to=[user_email])
                message.content_subtype = "html"
                message.send()
                return utils.CustomResponse.Success('Account Updated Successfully', status=status.HTTP_201_CREATED)
            except:
            # BREEVO OPTION TWO
                send_email(subject="Account Updated Successfully", body=email, sender_email=config('DEFAULT_FROM_EMAIL'), receiver_email=user_email, sender_name='Smartflowtech', reply_to=config('DEFAULT_FROM_EMAIL'))
            # send_email(subject="Email Updated Successfully", sender_email='<EMAIL>', sender_name='Smarteye', reply_to='<EMAIL>', body=email,receiver_email=user_email)
           
            result = push_notification(username)
            if result:
                    return utils.CustomResponse.Success(serializer_data.data, status=status.HTTP_200_OK)
            else:
                return utils.CustomResponse.Failure('Email not sent', status=status.HTTP_400_BAD_REQUEST)

        return utils.CustomResponse.Failure(serializer_data.errors, status=status.HTTP_400_BAD_REQUEST)

class LogoutView(APIView):
    def post(self, request):
        token = request.data['refresh']
        try:
            RefreshToken(token).blacklist()
        except:
            return utils.CustomResponse.Failure(error="Token is Blacklisted", status=status.HTTP_400_BAD_REQUEST)
        return utils.CustomResponse.Success(data="",status=status.HTTP_204_NO_CONTENT)