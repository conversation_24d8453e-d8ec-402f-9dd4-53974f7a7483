
import datetime
from django.db import connection
from backend import models
from . import utils as log_utils


def dictfetchall(cursor):
    columns = [col[0] for col in cursor.description]
    return [dict(zip(columns, row)) for row in cursor.fetchall()]


def format_to_dict(result):
    data = {}
    for each in result:
        data[each['Name']] = each['Device']
    return data


def new_summary(site_id, start_date, end_date):
    with connection.cursor() as c:
        query = """
                select total_amount,product,date,bs.Name as  site_name,bn.name as Nozzle_name, bp.Name, volume
                    from
                    (select round(sum(Raw_transaction_price_per_unit * Transaction_raw_volume)) as total_amount,round(sum(Transaction_raw_volume),3) as volume,
                    bt.Product_name as product,
                    date(Transaction_stop_time) as date,
                    Site_id ,
                    Device_id,
                    Nozzle_address
                    from backend_transactiondata bt
                    group by bt.Product_name ,
                    date(Transaction_stop_time),
                    Site_id ,
                    Device_id,
                    Nozzle_address
                    order by date desc) as sub
                    inner join backend_sites bs
                    on bs.Site_id = sub.site_id
                    inner join backend_pump bp
                    on bp.Site_id = bs.Site_id
                    and sub.device_id = bp.Device_id
                    inner join backend_nozzle bn
                    on bn.Pump_id = bp.id
                    and bn.Nozzle_address = sub.nozzle_address

                    WHERE bs.Site_id = %s
                    AND sub.date BETWEEN %s AND %s
                    order by date desc; -- report    
                    
                    """
        c.execute(query, [site_id, start_date, end_date])
        data = c.fetchall()
    return data


def new_summary_with_product_and_nozzle(site_id, start_date, end_date, nozzle_name, product):
    """
        SUMMARY WITH NOZZLE NAME AND PRODUCT
    """
    with connection.cursor() as c:
        query = """
                	select total_amount,product,date,bs.Name as  site_name,bn.name as Nozzle_name, bp.Name, volume
						from
						(select round(sum(Raw_transaction_price_per_unit * Transaction_raw_volume)) as total_amount,round(sum(Transaction_raw_volume),3) as volume,
						bt.Product_name as product,
						date(Transaction_stop_time) as date,
						Site_id ,
						Device_id,
						Nozzle_address
						from backend_transactiondata bt
						group by bt.Product_name ,
						date(Transaction_stop_time),
						Site_id ,
						Device_id,
						Nozzle_address
						order by date desc) as sub
						inner join backend_sites bs
						on bs.Site_id = sub.site_id
						inner join backend_pump bp
						on bp.Site_id = bs.Site_id
						and sub.device_id = bp.Device_id
						inner join backend_nozzle bn
						on bn.Pump_id = bp.id
						and bn.Nozzle_address = sub.nozzle_address

						WHERE bs.Site_id = %s
						AND sub.date BETWEEN %s AND %s
						AND bn.Name = %s
                        AND product = %s
				   
						order by date desc; -- report 
                    """
        c.execute(query, [site_id, start_date, end_date, nozzle_name, product])
        data = c.fetchall()
    return data


def new_summary_with_product(site_id, start_date, end_date, product):
    """
        SUMMARY WITH PRODUCT 
    """
    with connection.cursor() as c:
        query = """
                	select total_amount,product,date,bs.Name as  site_name,bn.name as Nozzle_name, bp.Name, volume
						from
						(select round(sum(Raw_transaction_price_per_unit * Transaction_raw_volume)) as total_amount,round(sum(Transaction_raw_volume),3) as volume,
						bt.Product_name as product,
						date(Transaction_stop_time) as date,
						Site_id ,
						Device_id,
						Nozzle_address
						from backend_transactiondata bt
						group by bt.Product_name ,
						date(Transaction_stop_time),
						Site_id ,
						Device_id,
						Nozzle_address
						order by date desc) as sub
						inner join backend_sites bs
						on bs.Site_id = sub.site_id
						inner join backend_pump bp
						on bp.Site_id = bs.Site_id
						and sub.device_id = bp.Device_id
						inner join backend_nozzle bn
						on bn.Pump_id = bp.id
						and bn.Nozzle_address = sub.nozzle_address

						WHERE bs.Site_id = %s
						AND sub.date BETWEEN %s AND %s
                        AND product = %s
				   
						order by date desc; -- report 
                    """
        c.execute(query, [site_id, start_date, end_date, product])
        data = c.fetchall()
    return data


def new_summary_with_nozzle(site_id, start_date, end_date, nozzle_name):
    """
        SUMMARY WITH NOZZLE NAME
    """
    with connection.cursor() as c:
        query = """
                	select total_amount,product,date,bs.Name as  site_name,bn.name as Nozzle_name, bp.Name, volume
						from
						(select round(sum(Raw_transaction_price_per_unit * Transaction_raw_volume)) as total_amount,round(sum(Transaction_raw_volume),3) as volume,
						bt.Product_name as product,
						date(Transaction_stop_time) as date,
						Site_id ,
						Device_id,
						Nozzle_address
						from backend_transactiondata bt
						group by bt.Product_name ,
						date(Transaction_stop_time),
						Site_id ,
						Device_id,
						Nozzle_address
						order by date desc) as sub
						inner join backend_sites bs
						on bs.Site_id = sub.site_id
						inner join backend_pump bp
						on bp.Site_id = bs.Site_id
						and sub.device_id = bp.Device_id
						inner join backend_nozzle bn
						on bn.Pump_id = bp.id
						and bn.Nozzle_address = sub.nozzle_address

						WHERE bs.Site_id = %s
						AND sub.date BETWEEN %s AND %s
						AND bn.Name = %s
				   
						order by date desc; -- report 
                    """
        c.execute(query, [site_id, start_date, end_date, nozzle_name])
        data = c.fetchall()
    return data


def site_and_period(site_id, start, end):
    """
        SITE AND PERIOD QUERY 
    """
    with connection.cursor() as c:
        query = """
                	select transaction_stop_time,bn.name as Nozzle_name,bt.Raw_transaction_price_per_unit,round(Transaction_raw_volume *
                    raw_transaction_price_per_unit) as total,bp.Name as pump_name,
                    bs.Name as site_name,bp1.name as Product_name,Transaction_raw_volume, bs.Site_id
                    from backend_transactiondata bt
                    inner join backend_pump bp
                    on bp.Device_id = bt.Device_id
                    and bp.Site_id = bt.Site_id
                    inner join backend_sites bs
                    on bs.Site_id = bp.Site_id
                    and bs.Site_id = bt.Site_id
                    inner join backend_nozzle bn
                    on bn.Pump_id = bp.id
                    and bt.Nozzle_address = bn.Nozzle_address
                    inner join backend_products bp1
                    on bp1.Product_id = bn.Product_id

                    where bs.Site_id= %s
                    and Transaction_stop_time between %s and %s

                    order by Transaction_stop_time desc;
                    """
        c.execute(query, [site_id, start, end])
        data = c.fetchall()
    return data


def site_period_and_product(site_id, start, end, product):
    """
        SITE PERIOD AND PRODUCT QUERY 
    """
    with connection.cursor() as c:
        query = """
                	select transaction_stop_time,bn.name as Nozzle_name,bt.Raw_transaction_price_per_unit,round(Transaction_raw_volume *
                    raw_transaction_price_per_unit) as total,bp.Name as pump_name,
                    bs.Name as site_name,bp1.name as Product_name,Transaction_raw_volume, bs.Site_id
                    from backend_transactiondata bt
                    inner join backend_pump bp
                    on bp.Device_id = bt.Device_id
                    and bp.Site_id = bt.Site_id
                    inner join backend_sites bs
                    on bs.Site_id = bp.Site_id
                    and bs.Site_id = bt.Site_id
                    inner join backend_nozzle bn
                    on bn.Pump_id = bp.id
                    and bt.Nozzle_address = bn.Nozzle_address
                    inner join backend_products bp1
                    on bp1.Product_id = bn.Product_id

                    where bs.Site_id= %s
                    and Transaction_stop_time between %s and %s
                    and Product_name = %s
                    order by Transaction_stop_time desc;
                    """
        c.execute(query, [site_id, start, end, product])
        data = c.fetchall()
    return data


def site_period_and_nozzle(site_id, start, stop, nozzle):
    """
        SITE PERIOD AND NOZZLE QUERY
    """
    with connection.cursor() as c:
        query = """
                	select transaction_stop_time,bn.name as Nozzle_name,bt.Raw_transaction_price_per_unit,round(Transaction_raw_volume *
                    raw_transaction_price_per_unit) as total,bp.Name as pump_name,
                    bs.Name as site_name,bp1.name as Product_name,Transaction_raw_volume, bs.Site_id
                    from backend_transactiondata bt
                    inner join backend_pump bp
                    on bp.Device_id = bt.Device_id
                    and bp.Site_id = bt.Site_id
                    inner join backend_sites bs
                    on bs.Site_id = bp.Site_id
                    and bs.Site_id = bt.Site_id
                    inner join backend_nozzle bn
                    on bn.Pump_id = bp.id
                    and bt.Nozzle_address = bn.Nozzle_address
                    inner join backend_products bp1
                    on bp1.Product_id = bn.Product_id

                    where bs.Site_id= %s
                    and Transaction_stop_time between %s and %s
                    and bn.name = %s


                    order by Transaction_stop_time desc;
                    """
        c.execute(query, [site_id, start, stop, nozzle])
        data = c.fetchall()
    return data


def site_period_product_and_nozzle(site_id, start, end, product, nozzle):
    """
        SITE PERIOD PRODUCT AND NOZZLE
    """
    with connection.cursor() as c:
        query = """
                    select transaction_stop_time,bn.name as Nozzle_name,bt.Raw_transaction_price_per_unit,round(Transaction_raw_volume *
                    raw_transaction_price_per_unit) as total,bp.Name as pump_name,
                    bs.Name as site_name,bp1.name as Product_name,Transaction_raw_volume, bs.Site_id
                    from backend_transactiondata bt
                    inner join backend_pump bp
                    on bp.Device_id = bt.Device_id
                    and bp.Site_id = bt.Site_id
                    inner join backend_sites bs
                    on bs.Site_id = bp.Site_id
                    and bs.Site_id = bt.Site_id
                    inner join backend_nozzle bn
                    on bn.Pump_id = bp.id
                    and bt.Nozzle_address = bn.Nozzle_address
                    inner join backend_products bp1
                    on bp1.Product_id = bn.Product_id

                    where bs.Site_id= %s
                    and Transaction_stop_time between %s and %s
                    and Product_name = %s
                    and bn.name = %s


                    order by Transaction_stop_time desc;
                	
                    """
        c.execute(query, [site_id, start, end, product, nozzle])
        data = c.fetchall()
    return data


def new_summary_product(site_id, start_date, end_date, product_type):
    with connection.cursor() as c:
        query = """
                SELECT
                    sub.computed_total,
                    sub.date,
                    sub.Device_id,
                    sub.Nozzle_address,
                    bs.name, 
                    sub.Product_name, 
                    sub.Pump_mac_address
                FROM
                    (
                        SELECT
                            SUM(bt.Transaction_raw_volume * bt.Raw_transaction_price_per_unit) AS computed_total,
                            DATE(bt.transaction_stop_time) AS date,
                            bt.Device_id,
                            bt.Nozzle_address,
                            bt.Site_id, -- Include Site_id for joining
                            bt.Product_name, 
                            bt.Pump_mac_address
                        FROM
                            backend_transactiondata bt
                        GROUP BY
                            DATE(bt.transaction_stop_time),
                            bt.Device_id,
                            bt.Nozzle_address,
                            bt.Site_id, -- Add Site_id to GROUP BY
                            bt.Product_name, 
                            bt.Pump_mac_address
                    ) AS sub
                INNER JOIN
                    backend_sites bs
                    ON bs.Site_id = sub.Site_id

                WHERE bs.Site_id = %s
                AND sub.date BETWEEN %s AND %s
                AND sub.Product_name = %s
                ORDER BY
                    sub.date DESC;
            """
        c.execute(query, [site_id, start_date, end_date, product_type])
        data = c.fetchall()
    return data


def get_today_sales(today_date, site_id):
    query = """
                SELECT * FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Site_id="{}"  
                """.format(today_date, site_id)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()  # ((5793512.888999994,),)
        # data = data[0][0]
    return data


def get_total_value(get_date, mac_address, nozzle_add):
    query = """
                SELECT SUM(Transaction_raw_amount) total_value
                FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Pump_mac_address ="{}" AND Nozzle_address = "{}";  
                """.format(get_date, mac_address, nozzle_add)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        data = data[0][0]
    return data


def get_pms_price_per_unit(get_date, site):
    query = f"""
                SELECT Raw_transaction_price_per_unit
                FROM backend_transactiondata 
                WHERE Product_name = "PMS" AND Site_id = {site} Order By Raw_transaction_price_per_unit Desc limit 1;  
                """
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        data = data[0][0]
    return data


def get_pms_total_volume(get_date, site):
    query = """

                SELECT  SUM(Transaction_raw_volume) as total_volume, SUM(computed_total) as pms_total_value

                FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Site_id ="{}" AND Product_name = "PMS";  
                """.format(get_date, site)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        if data:
            if data[0][0] == None:
                data = ((0.0, 0.0),)
                return data
        else:
            data = ((0.0, 0.0),)
    return data


def get_ago_price_per_unit(get_date, site):
    try:
        query = f"""
                    SELECT Raw_transaction_price_per_unit
                    FROM backend_transactiondata
                    WHERE Product_name = "AGO" AND Site_id = {site} Desc limit 1;
                    """
        with connection.cursor() as c:
            c.execute(query)
            data = c.fetchall()
            if not data:
                data = 0.00
                return data
            data = data[0][0]
    except Exception as e:
        print(e, "Error")
    return data


def get_ago_total_volume(get_date, mac_address):
    query = """
                SELECT SUM(Transaction_raw_volume) ago_total_volume, SUM(computed_total) as ago_total_value
                FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Site_id ="{}" AND Product_name = "AGO";  
                """.format(get_date, mac_address)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        if data:
            if data[0][0] == None:
                data = ((0.0, 0.0),)
                return data
        else:
            data = ((0.0, 0.0),)
    return data


def get_cng_total_volume(get_date, mac_address):
    query = """
                SELECT SUM(Transaction_raw_volume) ago_total_volume, SUM(computed_total) as ago_total_value
                FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Site_id ="{}" AND Product_name = "CNG";  
                """.format(get_date, mac_address)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        if data:
            if data[0][0] == None:
                data = ((0.0, 0.0),)
                return data
        else:
            data = ((0.0, 0.0),)
    return data


def get_dpk_price_per_unit(get_date, site):
    query = f"""
                SELECT Raw_transaction_price_per_unit
                FROM backend_transactiondata
                WHERE Product_name = "DPK" AND Site_id = {site} Order By Raw_transaction_price_per_unit Desc limit 1;
                """
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        if not data:
            data = 0.0
            return data
        else:
            data = data[0][0]
    return data


def get_dpk_total_volume(get_date, mac_address):
    query = """
                SELECT SUM(Transaction_raw_volume) dpk_total_volume, SUM(computed_total) as dpk_total_value
                FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Site_id ="{}" AND Product_name = "DPK";  
                """.format(get_date, mac_address)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        if data:
            if data[0][0] == None:
                data = ((0.0, 0.0),)
                return data
        else:
            data = ((0.0, 0.0),)
    return data


def get_total_volume(get_date, mac_address, nozzle_add):
    query = """
                SELECT SUM(Transaction_raw_volume) total_volume
                FROM backend_transactiondata 
                WHERE Transaction_stop_time LIKE "{}%" AND Pump_mac_address ="{}" AND Nozzle_address = {};  
                """.format(get_date, mac_address, nozzle_add)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        if data:
            if data[0][0] == None:
                data = 0.0
                return data
        data = data[0][0]
    return data


def get_pump_status(mac_address):
    status = models.Devices.objects.filter(
        Device_unique_address=mac_address).values('last_seen')
    status_time = datetime.datetime.strptime(
        f"{status[0]['last_seen']:%Y-%m-%d %H:%M:%S}", '%Y-%m-%d %H:%M:%S')
    return_status = 'online'
    # calculate the time difference between the input time and the current time
    time_diff_status = datetime.datetime.now() - status_time

    # compare the time difference with the thresholds and return the status accordingly
    if time_diff_status < datetime.timedelta(hours=2):
        return_status = 'online'
    if time_diff_status > datetime.timedelta(hours=4):
        return_status = 'idle'
    if time_diff_status > datetime.timedelta(hours=24):
        return_status = 'offline'
    if time_diff_status > datetime.timedelta(hours=72):
        return_status = 'inactive'
    return return_status


def update_status(mac_address):
    todays_date = datetime.datetime.now()

    query = f"""
                UPDATE backend_devices SET last_seen = "{todays_date}"
                WHERE Device_unique_address = "{mac_address}";  
            """
    try:
        with connection.cursor() as c:
            c.execute(query)
        return True
    except:
        return False


def join_pump_device(site_id):
    query = """
                SELECT P.id, P.Name, D.Device_unique_address, D.Device_id, P.Pumpbrand_id, P.Nozzle_count
                FROM backend_pump P
                JOIN backend_devices D ON P.Device_id = D.Device_id
                WHERE P.Site_id="{}";  
                """.format(site_id)
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        data = list(data)
    return data


def get_remote_configs(mac_address):
    query = f"""
                SELECT p.Site_id, p.Device_id, p.Nozzle_count, p.Pump_protocol, n.Nozzle_address_hex_code, pc.New_price, pc.Scheduled_time, n.Decimal_setting_price_unit, n.Decimal_setting_amount, n.Decimal_setting_volume 
                FROM backend_nozzle n, backend_pump p, backend_devices d, backend_pricechangerequestdata pc 
                WHERE d.Device_id = p.Device_id AND p.id = n.Pump_id AND d.Device_unique_address = "{mac_address}" AND pc.Approved ={True}  AND n.Product_id = pc.Product_id AND p.Site_id = pc.Site_id ORDER BY pc.db_fill_time DESC LIMIT 2;  
            """
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchall()
        configs = list(data)
        dic_configs = []
        for config in configs:
            Price = config[5]
            if not Price or Price == None:
                Price = "0.00"
            Price_time = config[6]
            if not Price_time or Price_time == None:
                Price_time = datetime.now()
            pic_remote_config = {
                "Site_id": config[0],
                "Device_id": config[1],
                "Nozzle_count": config[2],
                "Pump_protocol": config[3],
                "Nozzle_address": config[4],
                "Price": Price,
                "Price_time": Price_time,
                "Decimal_setting_price_unit": config[7],
                "Decimal_setting_amount": config[8],
                "Decimal_setting_volume": config[9]
            }
            dic_configs.append(pic_remote_config)
    return dic_configs


def get_pumps():
    with connection.cursor() as c:
        query = """
                    SELECT backend_pump.id, backend_pump.Name, backend_pump.Pump_protocol, backend_pump.Nozzle_count, backend_pump.Note, backend_pump.Activate, backend_pump.Pushed_to_device, backend_devices.Device_id, backend_devices.Name, backend_devices.Updated_at, backend_devices.Phone_number, backend_devices.Created_at, backend_devices.Device_unique_address, backend_devices.Deleted_at, backend_devices.Active, backend_devices.Available, backend_devices.transmit_interval, backend_devices.Company_id, backend_devices.ForPump, backend_devices.Used, backend_devices.last_seen, backend_sites.Site_id,backend_sites.Name, backend_sites.State, backend_sites.City, backend_sites.Address, backend_sites.Site_type, backend_sites.Notes, backend_sites.SIM_provided_details, backend_sites.Contact_person_phone, backend_sites.Contact_person_designation, backend_sites.Contact_person_mail,backend_sites.Contact_person_phone, backend_sites.Created_at, backend_sites.Updated_at, backend_sites.Deleted_at, backend_sites.Active, backend_sites.Communication_status, backend_sites.Communication_update_time, backend_sites.Company_id, backend_sites.Device_id, backend_sites.Country, backend_sites.Critical_level_mail, backend_sites.Number_of_tanks, backend_sites.Reorder_mail, backend_sites.Latitude, backend_sites.Location_status, backend_sites.Longitude, backend_sites.Email_Notification, backend_sites.genhours_access, backend_sites.smartpump_access, backend_sites.smarttank_access, backend_sites.country_id , backend_pump.use_decimal_config, backend_pumpbrand.* from backend_pump 
                    left join backend_devices on backend_pump.Device_id=backend_devices.Device_id 
                    left join backend_sites on backend_pump.Site_id=backend_sites.Site_id
                    left join backend_pumpbrand on backend_pump.Pumpbrand_id=backend_pumpbrand.id;
                """
        c.execute(query)
        data = c.fetchall()
    return list(data)


def get_nozzle(id):
    with connection.cursor() as c:
        query = f"""
                    SELECT * FROM backend_nozzle where Pump_id="{id}"; 
                """
        c.execute(query)
        data = c.fetchall()
    return list(data)


def get_product(product_id):
    with connection.cursor() as c:
        query = f"""
                    SELECT Name FROM backend_products where Product_id = "{product_id}"; 
                """
        c.execute(query)
        data = c.fetchall()
    return list(data)


def get_remote_config(data_key):
    query = f"""
            SELECT * FROM remote_config_service.backendConfig_configdata where mac_address = "{data_key}"; 
            """
    with connection.cursor() as c:
        data = c.execute(query)
    return data


def update_remote_config(data_key, cnv_json):
    query = """
                UPDATE remote_config_service.backendConfig_configdata SET remote_config = %s, db_fill_time = NOW()
                WHERE mac_address = %s;  
            """
    with connection.cursor() as c:
        c.execute(query, (cnv_json, data_key))
    return


def insert_remote_config(data_key, cnv_json):
    query = """INSERT INTO remote_config_service.backendConfig_configdata (mac_address, remote_config, db_fill_time) VALUES (%s, %s, NOW())"""
    with connection.cursor() as c:
        c.execute(query, (data_key, cnv_json))
    return


def get_pump_status(mact_address):
    query = f"""
            SELECT last_seen FROM backend_devices where Device_unique_address = "{mact_address}";
            """
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchone()
    return data[0] if data else None


def insert_pump_mode_switch_log(pump_status, device_id, date_time, site_id):
    sql = """
        INSERT INTO pump_mode_switch_logs (pump_status, device_id, date_time, created_at, updated_at,site_id) 
        VALUES (%s, %s, %s, NOW(), NOW(),%s);
        """
    with connection.cursor() as c:
        c.execute(sql, (pump_status, device_id, date_time, site_id))
    return


def update_pump_switch_status(mac_address, status):
    query = f"""
            UPDATE backend_devices SET pump_switch_status = "{status}" WHERE Device_unique_address = "{mac_address}";
            """
    with connection.cursor() as c:
        c.execute(query)
    return


def get_pump_mode_status(device_id):
    query = f"""
            SELECT * FROM pump_mode_switch_logs WHERE device_id = "{device_id[0]}" ORDER BY date_time DESC LIMIT 1;
            """
    with connection.cursor() as c:
        c.execute(query)
        data = c.fetchone()
    return data[2] if data else None


def get_all_pump_mode_status(site, start_date, end_date, device):
    query = """
        SELECT * FROM pump_mode_switch_logs 
        WHERE site_id = %s 
        AND date_time BETWEEN %s AND %s 
        AND device_id = %s 
        ORDER BY date_time DESC;
    """
    with connection.cursor() as cursor:
        cursor.execute(query, [site, start_date, end_date, device])
        result = cursor.fetchall()
    return result
