from backend import models
from datetime import date, datetime, timedelta
import datetime as datatimestr
from django.db.models import Sum
from backend.currency.utils import get_currency
from django.core.mail import send_mail, EmailMessage
from django.template import loader
from django.utils import timezone

from backend.smart_pump.queries import get_pump_mode_status, new_summary, get_total_volume, insert_remote_config, join_pump_device, get_pump_status, update_remote_config, site_and_period, site_period_and_product, site_period_and_nozzle, site_period_product_and_nozzle
from .serializer import TransactionDataWithPumpNameSerializer
from backend import smart_pump_celery_utils as smart_utils
from backend import utils
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import json
from django.core.serializers.json import DjangoJSONEncoder
from datetime import datetime
from sigfig import round
from decouple import config
from .serializer import TransactionDataSerializer
from django.db import connection
import redis
import mysql.connector
# import MySQLdb as mdb
from django.conf import settings
from io import StringIO
import csv


def send_sales_summary_new(site_id, start_date, end_date, mail_receiver_name, mail_receiver):
    """
        FUNCTION TO SEND REPORT FOR THE NEW SMARTPUMP CNG IMPLEMENTATION
    """
    data = new_summary(site_id, start_date, end_date)
    final_response = []
    for resp in data:
        respDict = {
            "date": resp[2],
            "product": resp[1],
            "nozzle": resp[4],
            "total_volume": '{:,.2f}'.format(resp[6]),
            "total_value(\u20a6)": '{:,.2f}'.format(resp[0]),
            "site": resp[3],
            "pump_name": resp[5]

        }
        final_response.append(respDict)
        csvfile = StringIO()
        field_names = ['date', 'product', 'nozzle', 'total_volume',
                       'total_value(\u20a6)', 'pump_name', 'site']
        writer = csv.DictWriter(csvfile, fieldnames=field_names)
        writer.writeheader()
        writer.writerows(final_response)

    site_name = models.Sites.objects.get(Site_id=site_id)
    # SEND THE GENERATED DATA TO THE USER EMAIL
    email_template_name = 'sales_report_email_template.html'
    template_context = {
        "full_username": mail_receiver_name,
        "site_name": f'{site_name}',
        "start_date": start_date,
        "end_date": end_date,
        "current_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    emailmsg = loader.render_to_string(email_template_name, template_context)
    email = EmailMessage('Sales Report', emailmsg,
                         '<EMAIL>', [mail_receiver])
    email.content_subtype = "html"  # Main content is now text/html
    email.attach('sales-report.csv', csvfile.getvalue(), 'text/csv')
    email.send()
    return True


def send_sales_summary_report(*args):
    email_receiver, mail_receiver_name, dateRange, Site_id, passed_products = args
    generated_summary_report = getSummaryReport(
        Site_id, passed_products, dateRange)
    all_date_summary_data = generated_summary_report.get('SummaryReport')

    summary_headers = all_date_summary_data[0].keys()
    # replace none value with 0.00
    formated_date_summary_data = utils.replace_none_with_zero(
        all_date_summary_data)
    csvfile = StringIO()
    writer = csv.DictWriter(csvfile, fieldnames=summary_headers)
    sales_totalizer = utils.generateSalesSummaryProductTotalizer(
        formated_date_summary_data)
    writer.writeheader()
    writer.writerows(formated_date_summary_data + sales_totalizer)
    # insert blank row
    # writer.writerows([{'date': ''}])

    site_name = models.Sites.objects.get(Site_id=Site_id)
    email_template_name = 'sales_report_email_template.html'
    template_context = {
        "full_username": mail_receiver_name,
        "site_name": f'{site_name}',
        "start_date": dateRange[0],
        "end_date": dateRange[-1],
        "current_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    emailmsg = loader.render_to_string(email_template_name, template_context)
    # update this mail to email_receiver i.e f'{email_receiver}' test email is working
    # msg = requests.post(
    #     config("MAIL_BASE_URL"),
    #     auth=("api",
    #     config("MAILGUN_SECRET_KEY")),
    #     # files=[("attachment", 'sales-report.csv', csvfile.getvalue())],
    #     data = {"from": config("MAIL_SENDER"), "to": [f'{email_receiver}'], "subject": 'Sales Report', "text": emailmsg}
    # )
    # if msg.status_code == 200:
    #     print("Yes")
    email = EmailMessage('Sales Report', emailmsg,
                         '<EMAIL>', [f'{email_receiver}'],)
    email.content_subtype = "html"  # Main content is now text/html
    email.attach('sales-report.csv', csvfile.getvalue(), 'text/csv')
    email.send()


def createProductPriceHistory(request):
    try:
        # Get schedule time from either field
        schedule_time = request.data.get('Schedule Time')
        if not schedule_time:
            schedule_time = request.data.get('Scheduled Time')

        try:
            # First try parsing as is
            scheduled_time = datetime.strptime(
                schedule_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # Try parsing with AM/PM format
                scheduled_time = datetime.strptime(
                    schedule_time, "%d/%m/%Y %I:%M %p")
            except ValueError:
                return False, f"Invalid datetime format: {schedule_time}"

        try:
            # Get price
            new_price = request.data.get('New Price')
            if not new_price:
                new_price = request.data.get('New_price')

            # Get site ID
            site_id = request.data.get('site_details')
            if not site_id:
                site_id = request.data.get('Site')

            # Get product ID - prioritize product_details
            product_id = None
            if 'product_details' in request.data:
                product_id = request.data['product_details'].get('Product_id')
            else:
                # If no product_details, try direct Product field
                product = request.data.get('Product')
                if isinstance(product, int):
                    product_id = product
                elif isinstance(product, str) and product.isdigit():
                    product_id = int(product)
                else:
                    # Try to find product by name/code
                    try:
                        product_obj = models.Products.objects.get(Name=product)
                        product_id = product_obj.Product_id
                    except models.Products.DoesNotExist:
                        return False, f"Product not found: {product}"

            # Validate required data
            if not all([new_price, site_id, product_id]):
                return False, "Missing required fields: price, site, or product"

            # Get model instances
            site = models.Sites.objects.get(Site_id=site_id)
            product = models.Products.objects.get(Product_id=product_id)
            approver = request.user
            initial = False

            # Create price history
            product_price_history = models.ProductPriceHistory(
                Site=site,
                Price=new_price,
                Sheduled_time=scheduled_time,
                Approved_by=approver,
                Product=product,
                Initial=initial
            )
            product_price_history.save()
            return True, "Price history created successfully"

        except models.Sites.DoesNotExist:
            return False, f"Site with id {site_id} not found"
        except models.Products.DoesNotExist:
            return False, f"Product with id {product_id} not found"
        except Exception as e:
            print(f"Error saving price history: {e}")
            return False, f"Error saving price history: {str(e)}"

    except Exception as e:
        print(f"Error in createProductPriceHistory: {e}")
        return False, str(e)


def updateCompanyPriceOnProductPriceHistory(request):
    new_price = request.data['New Price']
    scheduled_time = datetime.strptime(
        request.data['Schedule Time'], "%Y-%m-%d %H:%M:%S")
    product_id = request.data['product_details']['Product_id']
    approver = request.user
    company_id = request.data['Company']
    company = models.Companies.objects.get(Company_id=company_id)
    product = models.Products.objects.get(Product_id=product_id)
    sites_in_company = models.Sites.objects.filter(Company=company_id)

    initial = True
    for each_site in sites_in_company:
        # Check if it is first price for company and site
        if models.ProductPriceHistory.objects.filter(Product=product_id, Company=company_id, Site=each_site.Site_id).exists():
            initial = False
        site_instance = models.Sites.objects.get(Site_id=each_site.Site_id)
        product_price_history = models.ProductPriceHistory(
            Company=company, Site=site_instance, Price=new_price, Sheduled_time=scheduled_time, Approved_by=approver, Product=product, Initial=initial)

        product_price_history.save()


def changeNozzlePrice(data):
    new_price = data['New Price']
    scheduled_time = datetime.strptime(
        data['Schedule Time'], "%y-%m-%d %I:%M:%S %p")
    product_id = data['product_details']['Product_id']
    site = data['site_details']['Site_id']

    pumps_in_site = models.Pump.objects.filter(Site=site).values()
    site = models.Sites.objects.get(Site_id=site)
    product = models.Products.objects.get(Product_id=product_id)

    for each_pump in pumps_in_site:
        devices = models.Devices.objects.filter(
            pk=each_pump['Device_id']).values()
        nozzles = models.Nozzle.objects.filter(Pump=each_pump['id']).values()
        mac_address = devices[0]['Device_unique_address']
        for nozzle in nozzles:
            if nozzle['Product_id'] == product_id:
                Nozzle_address = nozzle['Nozzle_address']
                newPriceChange = models.PriceChange(Site=site, Product=product, New_price=new_price, mac_address=mac_address,
                                                    Nozzle_address=Nozzle_address, Note="", Scheduled_time=scheduled_time, Received=False, Approved=True, Rejected=False)
                newPriceChange.save()


def getPumpOnlineStatus(mac_address):
    '''
    This connection is important if we need to always see the status of the pump/tank online. 
    '''
    r = redis.Redis(host=config('REDIS_HOST'), port=config('REDIS_PORT'), db=0)
    return r.get(f'pump_online_{mac_address}')


def get_pump_switch_status(config_time_diff_minutes, config_time_diff_hours, transaction_time_diff, pump_switch_status):
    if pump_switch_status == 0:
        return ["Online (Manual)", "The pump is connected, but has been manually switched from automation to manual mode."]
    if config_time_diff_minutes < 6:
        if transaction_time_diff <= 4 and pump_switch_status == 1:
            return ["Online (Active)", "The pump is connected to the cloud and actively communicating with the system."]
        elif transaction_time_diff > 4 and pump_switch_status == 1:
            return ["Online (Idle - No Transaction)", "The pump is connected but has not processed any transactions in the last 4 hours."]
    elif config_time_diff_hours >= 72:
        return ["Inactive (No Activity)", "The pump has remained offline for over 3 days and is marked as inactive."]
    elif config_time_diff_minutes >= 6:
        return ["Offline (Disconnected)", "The pump is not connected to the cloud and is unable to receive updates."]
    

def get_pump_status_old(mac_address):
    status = models.Devices.objects.filter(
        Device_unique_address=mac_address).values('last_seen')
    status_time = datetime.strptime(
        f"{status[0]['last_seen']:%Y-%m-%d %H:%M:%S}", '%Y-%m-%d %H:%M:%S')
    return_status = ['online', 'The pump is connected and is actively communicating with the cloud in the last two hours.']
    # calculate the time difference between the input time and the current time
    time_diff_status = datetime.now() - status_time

    # compare the time difference with the thresholds and return the status accordingly

    if time_diff_status < timedelta(hours=2):
        return_status = ['online', 'The pump is connected and is actively communicating with the cloud in the last two hours.']
    if time_diff_status > timedelta(hours=4):
        return_status = ['idle', 'The pump is connected but has not processed any transactions in the last 4 hours.']
    if time_diff_status > timedelta(hours=24):
        return_status = ['offline', 'The pump has not communicated with the cloud for over 24 hours and is marked as offline.']
    if time_diff_status > timedelta(hours=72):
        return_status = ['inactive', 'The pump has not communicated with the cloud for over 3 days and is marked as inactive.']
    return return_status

def getAllNozzleDetailsInSite(site_id):
    """
        This function returns all nozzle details in a site
        params:
            site_id (int): site id
    """
    response = []
    today_date = date.today()
    get_date = today_date
    site_name = models.Sites.objects.get(Site_id=site_id).Name
    get_pump_device = join_pump_device(site_id)
    get_pump_id = [item[0] for item in get_pump_device]
    nozzles = models.Nozzle.objects.filter(Pump_id__in=get_pump_id).values()
    for nozzle in nozzles:
        try:
            this_pump_name = [i[1]
                              for i in get_pump_device if i[0] == nozzle['Pump_id']]
            device_id = [i[3]
                         for i in get_pump_device if i[0] == nozzle['Pump_id']]
            nozzle_count = [i[5]
                            for i in get_pump_device if i[0] == nozzle['Pump_id']]
            set_pump_id = [i[4]
                           for i in get_pump_device if i[0] == nozzle['Pump_id']]
            pump_id = nozzle['Pump_id']
            pump_name = this_pump_name
            Pumpbrand = models.PumpBrand.objects.filter(
                id=set_pump_id[0]).values()
            nozzle_id, nozzle_name, nozzle_add = nozzle["id"], nozzle["Name"], nozzle["Nozzle_address"]
            product = models.Products.objects.filter(
                Product_id=nozzle['Product_id']).values('Name')
            product_name = product[0]['Name']
            mac_address = [i[2] for i in get_pump_device if i[0] == pump_id]
            transaction = models.TransactionData.objects.filter(
                Pump_mac_address=mac_address[0], Nozzle_address=nozzle["Nozzle_address"]).values().last()

            try:
                calculated_flow_rate = transaction['Transaction_raw_volume']/(datetime.strptime(
                    str(transaction['Transaction_stop_time']), "%Y-%m-%d %H:%M:%S").minute)
            except:
                calculated_flow_rate = 0.00
                pass

            total_volume = get_total_volume(
                get_date, mac_address[0], nozzle_add) or 0.00

            try:
                # Get last seen time as datetime
                last_time = get_pump_status(mac_address[0])
                current_time = datetime.now()

                # Time differences in hours and minutes
                config_time_diff_hours = (
                    current_time - last_time).total_seconds() / 3600
                config_time_diff_minutes = (
                    current_time - last_time).total_seconds() / 60

                if transaction and transaction['Transaction_stop_time']:
                    transaction_time_diff = (
                        current_time - transaction['Transaction_stop_time']).total_seconds() / 3600
                else:
                    transaction_time_diff = float('inf')

                pump_mode_status = get_pump_mode_status(device_id)
                
                # if pump_mode_status == None:
                #     last_seen = "Pump Mode (N/A)"
                if pump_mode_status == None:
                    last_seen = get_pump_status_old(mac_address[0])
                else:
                    last_seen = get_pump_switch_status(
                        config_time_diff_minutes, config_time_diff_hours, transaction_time_diff, pump_mode_status)

            except Exception as e:
                print("Error determining pump status:", e)
                last_seen = "Status (Not Set)"

            try:
                temp = {
                    'nozzle_address': nozzle['Nozzle_address'],
                    'nozzle_count': nozzle_count[0],
                    'pump_id': nozzle['Pump_id'],
                    'nozzle_name': nozzle_name,
                    'site_name': site_name,
                    'pump_name': pump_name[0],
                    'pump_brand': Pumpbrand[0]['Name'],
                    'product': product_name,
                    'calculated_flow_rate': round(calculated_flow_rate, 3),
                    'totalizer': transaction['Transaction_stop_pump_totalizer_volume'],
                    'raw_volume': transaction['Transaction_raw_volume'],
                    'price_per_unit': transaction['Raw_transaction_price_per_unit'],
                    'transaction_time': transaction['Transaction_stop_time'],
                    'status': last_seen if isinstance(last_seen, str) else last_seen[0],
                    'status_description': last_seen if isinstance(last_seen, str) else last_seen[1],
                    'last_updated_time': transaction['Transaction_stop_time'],
                    'total_volume': "{:,.3f}".format(total_volume),
                    'total_value': "{:,.3f}".format(total_volume * transaction['Raw_transaction_price_per_unit'])
                }
                response.append(temp)
                continue
            except:
                temp = {
                    'nozzle_address': nozzle['Nozzle_address'],
                    'nozzle_count': nozzle_count[0],
                    'pump_id': nozzle['Pump_id'],
                    'nozzle_name': nozzle_name,
                    'site_name': site_name,
                    'pump_name': pump_name[0],
                    'pump_brand': Pumpbrand[0]['Name'],
                    'product': product_name,
                    'calculated_flow_rate': round(calculated_flow_rate, 3),
                    'totalizer': 0.00,
                    'raw_volume': 0.00,
                    'price_per_unit': 0.00,
                    'transaction_time': "N/A",
                    'status': last_seen if isinstance(last_seen, str) else last_seen[0],
                    'status_description': last_seen if isinstance(last_seen, str) else last_seen[1],
                    'last_updated_time': "N/A",
                    'total_volume': "{:,.3f}".format(total_volume),
                    'total_value': "{:,.3f}".format(total_volume * 0.00)
                }
                response.append(temp)
        except:
            continue

    return response


def getNozzleDetails(data):
    response = []
    site_name = models.Sites.objects.get(Site_id=data['site_id']).Name
    pump_name = models.Pump.objects.get(id=data['pump_id']).Name
    nozzles = models.Nozzle.objects.filter(id=data['nozzle_id'])
    for nozzle in nozzles:
        this_pump = models.Pump.objects.get(id=nozzle.Pump_id)
        try:
            this_pump_mac = this_pump.Device.Device_unique_address
        except AttributeError:
            continue
        last_seen = getPumpOnlineStatus(this_pump_mac)
        product_name = models.Products.objects.get(
            Product_id=nozzle.Product_id).Name
        latest_transaction = models.TransactionData.objects.all().order_by(
            'Transaction_id').first()

        # latest_transaction = models.TransactionData.objects.filter(Site=site_id, Pump_mac_address=this_pump_mac, Nozzle_address=each_nozzle['Nozzle_address']).order_by('-Transaction_id').first()
        # Exclude the *continue* statement so that all nozzles are captured

        # if(not latest_transaction):
        #     continue
        try:
            nozzle_totalizer = latest_transaction.Transaction_stop_pump_totalizer_volume
            nozzle_price_per_unit = latest_transaction.Raw_transaction_price_per_unit
            nozzle_last_transact = latest_transaction.Transaction_stop_time
        # exception thrown bcoz no transaction found on the nozzle.
        except AttributeError:
            nozzle_totalizer = 'N/A'
            nozzle_price_per_unit = 'N/A'
            nozzle_last_transact = 'N/A'

        calculated_flow_rate = latest_transaction.Transaction_raw_volume / \
            (datetime.strptime(str(nozzle_last_transact), "%Y-%m-%d %H:%M:%S").minute)
        temp = {
            'pump_id': nozzles[0].Pump_id,
            'nozzle_name': nozzles[0].Name,
            'site_name': site_name,
            'pump_name': pump_name,
            'pump_brand': this_pump.Pumpbrand.Name,
            'product': product_name,
            'calculated_flow_rate': round(calculated_flow_rate, sigfigs=3),
            'totalizer': nozzle_totalizer,
            'raw volume': latest_transaction.Transaction_raw_volume,
            'price_per_unit': nozzle_price_per_unit,
            'transaction_time': nozzle_last_transact,
            'status': last_seen,
            'last_updated_time': latest_transaction.Uploaded_time,
        }
        response.append(temp)
    return response


def delaySendingReportStatus(dateRange, maxAllowedDays, reportDelayActive):
    if len(dateRange) > maxAllowedDays and reportDelayActive:
        return True
    else:
        return False


def getDateRange(request):
    sdate = datatimestr.datetime.strptime(
        request.GET['Start_time'], "%Y-%m-%d")   # start date
    edate = datatimestr.datetime.strptime(
        request.GET['End_time'], "%Y-%m-%d")   # end date

    def dates_bwn_twodates(start_date, end_date):
        for n in range(int((end_date - start_date).days + 1)):
            yield start_date + timedelta(n)

    return ([str(d).split(" ")[0] for d in dates_bwn_twodates(sdate, edate)])


def getSummaryForDate(Site_id, date, passed_products):
    # presentDateTransanctionsFilter = models.TransactionData.objects.filter(
    #     Site=Site_id, Transaction_stop_time__date=date)
    # presentDateTransactions = TransactionDataWithPumpNameSerializer(
    #     presentDateTransanctionsFilter, many=True)
    # transactions = {"date": f'{date}',
    #                 "transactions": presentDateTransactions.data}
    summary = {"date": date}

    def getAmountAndVolume(eachProduct):
        product_amount_with_volume = models.TransactionData.objects.filter(
            Product_name=eachProduct, Site=Site_id, Transaction_stop_time__date=date).aggregate(Sum('Transaction_raw_amount'), Sum('Transaction_raw_volume'))
        return [{f'{eachProduct.upper()}_N': product_amount_with_volume.get(
            'Transaction_raw_amount__sum')}, {f'{eachProduct.upper()}_L': product_amount_with_volume.get(
                'Transaction_raw_volume__sum')}]
    with ThreadPoolExecutor(max_workers=len(passed_products)) as executor:
        amount_with_volume_futures = [executor.submit(
            getAmountAndVolume,
            eachProduct) for eachProduct in passed_products]

        for price_volume_future in as_completed(amount_with_volume_futures):
            summary.update(price_volume_future.result()[0])
            summary.update(price_volume_future.result()[1])

    return {'summary': summary, 'transactions': [], }


def getSummaryReport(Site_id, passed_products, dateRange):

    summary_rpt = []
    summaryTransactionsForDate = []
    pms_price_per_unit = models.TransactionData.objects.filter(
        Product_name=passed_products[0]).values('Raw_transaction_price_per_unit').last()
    if not pms_price_per_unit:
        pms_price_per_unit = {'Raw_transaction_price_per_unit': 0.000}
    ago_price_per_unit = models.TransactionData.objects.filter(
        Product_name=passed_products[1]).values('Raw_transaction_price_per_unit').last()
    if not ago_price_per_unit:
        ago_price_per_unit = {'Raw_transaction_price_per_unit': 0.000}
    dpk_price_per_unit = models.TransactionData.objects.filter(
        Product_name=passed_products[2]).values('Raw_transaction_price_per_unit').last()
    if not dpk_price_per_unit:
        dpk_price_per_unit = {'Raw_transaction_price_per_unit': 0.000}
    cng_price_per_unit = models.TransactionData.objects.filter(
        Product_name=passed_products[3]).values('Raw_transaction_price_per_unit').last()
    if not cng_price_per_unit:
        cng_price_per_unit = {'Raw_transaction_price_per_unit': 0.000}
    # using thread to get transactions and summary for each date
    with ThreadPoolExecutor(max_workers=len(dateRange)) as executor:
        report_futures = [executor.submit(
            getSummaryForDate,
            Site_id, date, passed_products) for date in dateRange]

        for future in as_completed(report_futures):
            summary_rpt.append(future.result().get('summary'))
            summaryTransactionsForDate.append(
                future.result().get('transactions'))

    # sort summary report by date since thread is asynchronous
    summary_rpt.sort(key=lambda x: datetime.strptime(x['date'], '%Y-%m-%d'))
    currency_symbol = get_currency(Site_id)
    summaryReport = []
    for i in summary_rpt:
        dta = i['date']
        user = ""
        user_id = models.TransactionData.objects.filter(
            Transaction_stop_time__icontains=dta).values('user_id')
        if len(user_id) == 0:
            pass
        else:
            try:
                user_ = models.User.objects.filter(
                    id=user_id[0]["user_id"]).values("Name")
                user = user_[0]["Name"]
            except:
                pass
        PMS_N = i['PMS_N']
        PMS_L = i['PMS_L']
        AGO_N = i['AGO_N']
        AGO_L = i['AGO_L']
        DPK_N = i['DPK_N']
        DPK_L = i['DPK_L']
        CNG_N = i['CNG_N']
        CNG_L = i['CNG_L']
        if PMS_N == None:
            PMS_N = 0.000
        if PMS_L == None:
            PMS_L = 0.000
        if AGO_N == None:
            AGO_N = 0.000
        if AGO_L == None:
            AGO_L = 0.000
        if DPK_N == None:
            DPK_N = 0.000
        if DPK_L == None:
            DPK_L = 0.000
        if CNG_N == None:
            CNG_N = 0.00
        if CNG_L == None:
            CNG_L = 0.00
        # summaryReport.append({'date': i['date'], 'PMS_N': "{:,.3f}".format(PMS_L * pms_price_per_unit['Raw_transaction_price_per_unit']), 'PMS_L': "{:,.3f}".format(PMS_L), 'AGO_N': "{:,.3f}".format(AGO_L * ago_price_per_unit['Raw_transaction_price_per_unit']), 'AGO_L': "{:,.3f}".format(AGO_L), 'DPK_N': "{:,.3f}".format(DPK_L * dpk_price_per_unit['Raw_transaction_price_per_unit']), 'DPK_L': "{:,.3f}".format(DPK_L), "user":user})
        summaryReport.append({'date': i['date'], 'CNG_N': "{:,.3f}".format(CNG_L * cng_price_per_unit['Raw_transaction_price_per_unit']), 'CNG_L': "{:,.3f}".format(CNG_L), 'PMS_N': "{:,.3f}".format(PMS_L * pms_price_per_unit['Raw_transaction_price_per_unit']), 'PMS_L': "{:,.3f}".format(
            PMS_L), 'AGO_N': "{:,.3f}".format(AGO_L * ago_price_per_unit['Raw_transaction_price_per_unit']), 'AGO_L': "{:,.3f}".format(AGO_L), 'DPK_N': "{:,.3f}".format(DPK_L * dpk_price_per_unit['Raw_transaction_price_per_unit']), 'DPK_L': "{:,.3f}".format(DPK_L), "user": user})
    returnedData = {'SummaryReport': summaryReport,
                    'SummaryTransactionsForDate': summaryTransactionsForDate,
                    "currency": currency_symbol
                    }
    return returnedData


class Report:
    def __init__(self, noz_adr, site, mac_adr, time_range, products):
        self.Nozzle_address = noz_adr
        self.Site_id = site
        self.Pump_mac_address = mac_adr
        self.transaction_time = time_range
        self.products = products


class TransactionHistoryFactory:
    def __init__(self, noz_adr, site, mac_adr, time_range, products):
        self.Nozzle_address = noz_adr
        self.Site_id = site
        self.Pump_mac_address = mac_adr
        self.transaction_time = time_range
        self.products = products

    def get_transaction_history_type(self):
        if (self.Pump_mac_address[0]) == '' and (self.Nozzle_address[0]) == '' and self.products[0] == '':
            return SiteandPeriodTransaction(self.Nozzle_address, self.Site_id, self.Pump_mac_address, self.transaction_time, self.products)
        elif (self.Pump_mac_address[0]) == '' and (self.Nozzle_address[0]) == '' and self.products[0] != '':
            return SitePeriodProductTransaction(self.Nozzle_address, self.Site_id, self.Pump_mac_address, self.transaction_time, self.products)
        # elif (self.Pump_mac_address[0]) == '' and (self.Nozzle_address[0]) != '' and self.products[0] != '':
        #     return SitePeriodProductNozzleTransaction(self.Nozzle_address,self.Site_id,self.transaction_time,self.products)
        elif len(self.Nozzle_address) > 0 and len(self.Nozzle_address) > 0 and len(self.products) > 0:
            return SitePeriodNozzleTransaction(self.Nozzle_address, self.Site_id, self.Pump_mac_address, self.transaction_time, self.products)
        elif len(self.Nozzle_address) > 0 and len(self.products) > 0 and (self.Nozzle_address[0]) != '' and (self.products[0]) != '':
            return SitePeriodProductNozzleTransaction(self.Nozzle_address, self.Site_id, self.Pump_mac_address, self.transaction_time, self.products)


class SiteandPeriodTransaction(Report):
    def get_transactions(self):
        transactions = site_and_period(
            site_id=self.Site_id, start=self.transaction_time[0], end=self.transaction_time[1])
        return transactions


class SitePeriodProductTransaction(Report):
    def get_transactions(self):
        transactions = site_period_and_product(
            site_id=self.Site_id, start=self.transaction_time[0], end=self.transaction_time[1], product=self.products)
        return transactions


class SitePeriodNozzleTransaction(Report):
    def get_transactions(self):
        # transactions = (models.TransactionData.objects.filter(
        # Site=self.Site_id, Transaction_stop_time__range=self.transaction_time, Nozzle_address__in=self.Nozzle_address,Pump_mac_address__in=self.Pump_mac_address).order_by('-Transaction_stop_time'))
        transactions = site_period_and_nozzle(
            site_id=self.Site_id, start=self.transaction_time[0], stop=self.transaction_time[1], nozzle=self.Nozzle_address)
        return transactions


class SitePeriodProductNozzleTransaction(Report):
    def get_transactions(self):
        # transactions = (models.TransactionData.objects.filter(
        # Site=self.Site_id, Transaction_stop_time__range=self.transaction_time, Nozzle_address__in=self.Nozzle_address, Pump_mac_address__in=self.Pump_mac_address, Product_name__in=self.products).order_by('-Transaction_stop_time'))
        transactions = site_period_product_and_nozzle(
            site_id=self.Site_id, start=self.transaction_time[0], end=self.transaction_time[1], product=self.products, nozzle=self.Nozzle_address)
        return transactions


class SitePeriodProductNozzleTransactionTapnet(Report):
    def get_transactions(self):
        # transactions = (models.TransactionData.objects.filter(
        #     Site=self.Site_id, Transaction_stop_time__range=self.transaction_time, Nozzle_address__in=self.Nozzle_address, Product_name__in=self.products).order_by('-Transaction_stop_time'))
        transactions = site_period_product_and_nozzle(
            site_id=self.Site_id, start=self.transaction_time[0], end=self.transaction_time[1], product=self.products, nozzle=self.Nozzle_address)
        return transactions


def send_price_change_notification(price_change_request, designation, is_initial_price):
    # Use factory to get a Price Alarm notifier class based on the Approval Status,designation(site/company) and if 1st price
    notifier = smart_utils.PriceAlarmFactory(
        price_change_request, designation, is_initial_price).create_alarm_notifier()
    # trigger notifier notify method
    if notifier is not None:
        notifier.notify()


def send_remote_config(data):
    try:
        device_in_pump = [
            each.Device.Device_unique_address for each in models.Pump.objects.filter(Site_id=data)]
        if not device_in_pump:
            print("No devices found for site")
            return False

        data_final = []
        # download remote config for devices
        for each in device_in_pump:
            try:
                data_set = {}
                config = utils.make_request(each)
                if not config or 'data' not in config:
                    print(f"Invalid config response for device {each}")
                    continue
                data_set[each] = config['data']
                data_final.append(data_set)
            except Exception as e:
                print(f"Error getting config for device {each}: {str(e)}")
                continue

        if not data_final:
            print("No valid configurations found")
            return False
        # send downloaded remote config to remote config service
        for remote_config in data_final:
            try:
                for key, records in remote_config.items():
                    for record in records:
                        try:
                            dt = datetime.strptime(record['Price_time'].split('.')[
                                                   0], "%Y-%m-%dT%H:%M:%S")
                            record['Price_time'] = dt.strftime(
                                "%Y-%m-%dT%H:%M:%S")
                        except Exception as e:
                            # Use server time as fallback
                            record['Price_time'] = datetime.now().strftime(
                                "%Y-%m-%dT%H:%M:%S")

                json_object = json.dumps(remote_config)
                response = utils.send_remote_config_data(json_object)
                if not response:
                    print("Empty response from remote config service")
                    continue
            except Exception as e:
                print(f"Error processing remote config: {str(e)}")
                continue

        return False

    except Exception as e:
        print(f"Error in send_remote_config: {str(e)}")
        return False

# def send_remote_config(data, is_initial_price):
#     site_id = data
#     pumps = models.Pump.objects.filter(Site_id=site_id).values('Device')
#     device_unique_addresses = list(models.Devices.objects.filter(Device_id__in=pumps).values())
#     convert_json = json.dumps(device_unique_addresses, cls=DjangoJSONEncoder)
#     device_unique_addresses = json.loads(convert_json)
#     data_final = []
#     # Download remote config for devices
#     for unique_address in device_unique_addresses:
#         try:
#             device_unique_address = unique_address["Device_unique_address"]
#             data_set = {}
#             config = utils.make_request(device_unique_address)
#             data_set[device_unique_address] = config['data']
#             data_final.append(data_set)
#         except:
#             continue

#     # Send downloaded remote config to remote config service
#     for remote_config in data_final:
#         try:
#             json_object = json.dumps(remote_config)
#             data = json.loads(json_object)
#             data_key, data_value = list(data.keys()), list(data.values())
#             config_val = {"remote_config":data_value[0]}
#             cnv_json = json.dumps(config_val)
#             check_if_exist = get_remote_config(data_key[0])
#             if check_if_exist > 0:
#                 update_remote_config(data_key,cnv_json)
#             else:
#                 insert_remote_config(data_key,cnv_json)
#         except:
#             continue
#     return
