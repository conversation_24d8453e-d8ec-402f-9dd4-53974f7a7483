from django.db import connection
from backend import models


def get_users():
    with connection.cursor() as c:
        query = """
                    SELECT backend_user.last_login, backend_user.Name, backend_user.Email, backend_user.Phone_number, backend_user.Created_at, backend_user.Updated_at ,backend_user.is_active, backend_companies.Name As `Company Name`, backend_user.id, auth_group.name As `Group Name`, backend_user.Company_id, backend_user.Deleted_at
                    FROM backend_user
                    JOIN auth_group ON auth_group.id = backend_user.groups_id
                    JOIN backend_companies ON backend_companies.Company_id = backend_user.Company_id;
                """
        c.execute(query)
        data = c.fetchall()
    return list(data)


def get_users_no_role():
    with connection.cursor() as c:
        query = """
                   SELECT backend_user.Email, backend_user.Name,  backend_user.id FROM backend_user;
                """
        c.execute(query)
        data = c.fetchall()
    return list(data)

def save_users_profile(user_email,url_path,pk):
    """
        Save user profile
    """
    with connection.cursor() as c:
        query = f"""
                    INSERT INTO backend_userprofileimg (email, url_path, user_id)
                    VALUES ("{user_email}", "{url_path}", {pk});
                """
        c.execute(query)
    return 'Profile image saved successfully'