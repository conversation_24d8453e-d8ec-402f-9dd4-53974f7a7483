from datetime import datetime
from decouple import config
from rest_framework import status
from rest_framework.views import APIView
from rest_framework import generics

from drf_spectacular.utils import extend_schema
from ..roles.serializer import PermissionSerializer

from django.shortcuts import get_object_or_404
from django.template import loader
from django.contrib.auth import get_user_model
from django.core.mail import send_mail, EmailMessage
from backend.authentication.utils import send_email
from django.contrib.auth.tokens import default_token_generator


from backend.users.queries import get_users, save_users_profile, get_users_no_role

from .serializer import UserProfileSerializerImg, UserSerializer,ModulesSerializer
from .. import utils 
from backend import models
from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination


class ModulesList(generics.ListCreateAPIView):
    serializer_class = ModulesSerializer
    queryset = models.Modules.objects.all()

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data)

    def create(self, request, *args, **kwargs):
        response = super().create(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data, status.HTTP_201_CREATED)


class UserList(generics.ListCreateAPIView):
    """
    List all Users, or create a new user
    """
    serializer_class = UserSerializer
    queryset = get_user_model().objects.exclude(Company__Owned=True)
    # queryset = get_user_model().objects.all()

    # def list(self, request, *args, **kwargs):
    #     response = super().list(request, *args, **kwargs)
    #     return utils.CustomResponse.Success(response.data)
    def get(self, request):
        data = []
        users = get_users()
        for item in range(len(users)):
            try:
                if users[item][0] == None:
                    last_login = datetime.today()
                else:
                    last_login = f"{users[item][0]:%Y-%m-%d %H-%M-%S}"
            except:
                last_login = datetime.today()
                

            try:
                Created_at = f"{users[item][4]:%Y-%m-%d %H-%M-%S}"
            except:
                Created_at = datetime.today()

            try:
                Updated_at = f"{users[item][4]:%Y-%m-%d %H-%M-%S}"
            except:
                Updated_at = datetime.today()
                
            user = {
                "id": users[item][8],
                "Company": {"Name": users[item][7]},
                "Company_id":users[item][10],
                "Role": {"Name": users[item][9]},
                "last_login": last_login,
                "Name": users[item][1],
                "Email": users[item][2],
                "Phone_number": users[item][3],
                "Created_at": Created_at,
                "Updated_at": users[item][5],
                "Deleted_at": users[item][11],
                "is_active": users[item][6],
                "groups":users[item][9]
            
            }
            data.append(user)
        sortedArray = sorted(data,key=lambda x: datetime.strptime(x['Created_at'], '%Y-%m-%d %H-%M-%S'), reverse=True)
        
        # paginator = PageNumberPagination()
        # paginator.page_size = int(request.query_params.get('page_size', 20))
        
        # result_page = paginator.paginate_queryset(sortedArray, request)
        # return paginator.get_paginated_response(result_page)
        # return utils.CustomResponse.Success(sortedArray, status.HTTP_200_OK)
        paginator = LimitOffsetPagination()
        paginator.default_limit = int(request.query_params.get('page_size', 20))
        paginator.max_limit = 100
        result_page = paginator.paginate_queryset(sortedArray, request)
        return paginator.get_paginated_response(result_page)


    def post(self, request, *args, **kwargs):
        """
            Extra Legs of Validation to ensure only necessary DB hit occurs on the DB
        """

        if 'Company_id' not in list(request.data.keys()):
            return utils.CustomResponse.Failure( error="Company_id is required", status=400)

        elif 'group_id' not in list(request.data.keys()):
            return utils.CustomResponse.Failure(error="group_id is required", status=400)

        elif 'Phone_number' not in list(request.data.keys()):
            return utils.CustomResponse.Failure(error="Phone Number is required", status=400)

        elif 'Email' not in list(request.data.keys()):
            return utils.CustomResponse.Failure(error="Email Field is required", status=400)
            
        elif request.data['Company_id'] == '':
            return utils.CustomResponse.Failure(error="Company_id Cannot be None", status=400)
        
        elif type(request.data['Company_id']) != int:
            return utils.CustomResponse.Failure(error="Invalid Data type for Company_id", status=400)
        
        elif type(request.data['group_id']) != int:
            return utils.CustomResponse.Failure(error="Invalid Data type for group_id", status=400)
        
        elif request.data['Phone_number'] == '':
            return utils.CustomResponse.Failure(error="Phone Number cannot be null",status=400)
        elif type(request.data['Phone_number']) != str:
            return utils.CustomResponse.Failure(error="Invalid Data type for Phone Number", status=400)
        elif request.data['Email'] == '':
            return utils.CustomResponse.Failure(error="Email cannot be null",status=400)
        elif type(request.data['Email']) != str:
            return utils.CustomResponse.Failure(error="Invalid Data type for Email", status=400)
        elif type(request.data['Name']) != str:
            return utils.CustomResponse.Failure(error="Name must be of string type",status=400)
        else:
            try:
                models.Group.objects.get(id=request.data['group_id'])
                models.Companies.objects.get(Company_id=request.data['Company_id'])
            except models.Companies.DoesNotExist:
                return utils.CustomResponse.Failure(error="Company Does Not Exist",status=404)
            except models.Group.DoesNotExist:
                return utils.CustomResponse.Failure(error="Group Does Not Exist", status=404)

            response = super().create(request, *args, **kwargs)
            # send email to the user
            user=get_user_model().objects.get(Email=request.data['Email'])
            user_email = user.Email
            token = default_token_generator.make_token(user)
            uid = user.pk
            # create password reset object in db
            models.PasswordReset.objects.get_or_create(user_id=user.pk, token=token)

            # Use smarteye live link
            reset_link = (config('SMARTEYE_BASE_URL')+"#/auth/resetpassword/{uid}/{token}?reset=password").format(uid=uid, token=token)
            email_template_name = 'custom_email_template.html'
            email_template_reset = 'email_reset.html'
            template_context = {
                "username": user.get_username,
                "reset_link": reset_link
            }
            email = loader.render_to_string(email_template_name, template_context)
            reset_email = loader.render_to_string(email_template_reset, template_context)
            try:
                message = EmailMessage(subject="Smarteye Account Password Reset",body=reset_email,reply_to=[user_email],to=[user_email])
                message.content_subtype = "html"
                message.send()
                print("Email sent successfully, Using AWS SES")
                    
            except:
                # BREEVO OPTION TWO
                send_email(subject="Smarteye account Password Reset", body=reset_email, sender_email=config('DEFAULT_FROM_EMAIL'), receiver_email=user_email, sender_name='Smartflowtech', reply_to=config('DEFAULT_FROM_EMAIL'))
                print("Email sent successfully, Using Breevo")
        return utils.CustomResponse.Success(data=response.data,status=201)

class UserListv2(generics.ListAPIView):
    """
    List all Users, or create a new user
    """
    permission_classes = ()
    authentication_classes = ()
    serializer_class = UserSerializer
    queryset = get_user_model().objects.exclude(Company__Owned=True)

    def list(self, request, *args, **kwargs):
        data = []
        users = get_users()
        for item in range(len(users)):
            try:
                if users[item][0] == None:
                    last_login = datetime.today()
                else:
                    last_login = f"{users[item][0]:%Y-%m-%d %H-%M-%S}"
            except:
                last_login = datetime.today()
                

            try:
                Created_at = f"{users[item][4]:%Y-%m-%d %H-%M-%S}"
            except:
                Created_at = datetime.today()

            try:
                Updated_at = f"{users[item][4]:%Y-%m-%d %H-%M-%S}"
            except:
                Updated_at = datetime.today()
                
            user = {
                "id": users[item][8],
                "Company": {"Name": users[item][7]},
                "Company_id":users[item][10],
                "Role": {"Name": users[item][9]},
                "last_login": last_login,
                "Name": users[item][1],
                "Email": users[item][2],
                "Phone_number": users[item][3],
                "Created_at": Created_at,
                "Updated_at": users[item][5],
                "Deleted_at": Updated_at,
                "is_active": users[item][6],
                "groups":users[item][9]
            
            }
            data.append(user)
        sortedArray = sorted(data,key=lambda x: datetime.strptime(x['Created_at'], '%Y-%m-%d %H-%M-%S'), reverse=True)
        
        return utils.CustomResponse.Success(sortedArray, status.HTTP_200_OK)
    
    

class  UserPayload(APIView):
    # permission_classes = ()
    # authentication_classes = ()

    def get(self, request):
        data = []
        user = request.query_params.get('email')
        # get_user_info = models.User.objects.filter(Email=user).values()

        # _data = get_user_model().objects.filter(Email=user).values()

        # get permissions from the user role
        

   

        try:
            # get_user_info = models.User.objects.filter(Email=user).values()
            get_user_info = get_user_model().objects.filter(Email=user).values()
            user_id = get_user_info[0]['id']
            user_role = get_user_info[0]['groups_id']
            user_profile = models.UserProfileImg.objects.filter(user_id = user_id).values()
            
            if user_role == 4:
                company = models.Companies.objects.all().values()
                sites = models.Sites.objects.all().values()
                tanks = models.Tanks.objects.all().values()
                tanks_group = models.TankGroups.objects.all().values()
                pumps = models.Pump.objects.all().values()
                data.append({
                    "company":company,
                    "sites":sites,
                    "tanks":tanks,
                    "tanks_group":tanks_group,
                    "pumps":pumps,
                    "user_profile":user_profile
                })
            elif user_role == 3:
                company = models.Companies.objects.all().values()
                sites = models.Sites.objects.all().values()
                tanks = models.Tanks.objects.all().values()
                tanks_group = models.TankGroups.objects.all().values()
                pumps = models.Pump.objects.all().values()
                data.append({
                    "company":company,
                    "sites":sites,
                    "tanks":tanks,
                    "tanks_group":tanks_group,
                    "pumps":pumps,
                    "user_profile":user_profile
                })
            elif user_role == 2:
                company = models.Companies.objects.filter(Company_id=get_user_info[0]['Company_id']).values()
                sites = models.Sites.objects.filter(Company_id=get_user_info[0]['Company_id']).values()
                site_id=[sites_ids['Site_id'] for sites_ids in sites]
                tanks = models.Tanks.objects.filter(Site_id__in=site_id).values()
                tanks_group = models.TankGroups.objects.filter(Company_id=get_user_info[0]['Company_id']).values()
                pumps = models.Pump.objects.filter(Site_id__in=site_id).values()
                data.append({
                    "company":company,
                    "sites":sites,
                    "tanks":tanks,
                    "tanks_group":tanks_group,
                    "pumps":pumps,
                    "user_profile":user_profile
                })
            else:
                company = models.Companies.objects.filter(Company_id=get_user_info[0]['Company_id']).values()
                sites = models.Sites.objects.filter(Company_id=get_user_info[0]['Company_id']).values()
                site_id=[sites_ids['Site_id'] for sites_ids in sites]
                tanks = models.Tanks.objects.filter(Site_id__in=site_id).values()
                tanks_group = models.TankGroups.objects.filter(Company_id=get_user_info[0]['Company_id']).values()
                pumps = models.Pump.objects.filter(Site_id__in=site_id).values()
                data.append({
                    "company":company,
                    "sites":sites,
                    "tanks":tanks,
                    "tanks_group":tanks_group,
                    "pumps":pumps,
                    "user_profile":user_profile
                })
            return utils.CustomResponse.Success(data, status.HTTP_200_OK)
        except:
            return utils.CustomResponse.Failure(f"This user: {user} is not found on the DB", status.HTTP_404_NOT_FOUND)
            


class AllUserList(generics.ListAPIView):
    # serializer_class = UserSerializer
    # queryset = get_user_model().objects.all()
    authentication_classes = ()
    permission_classes = ()

    def list(self, request, *args, **kwargs):
        # response = super().list(request, *args, **kwargs)
        # use the sql query to get all users with no roles
        data = []
        users = get_users_no_role()
        for item in range(len(users)):
            user = {
                "Email": users[item][0],
                "Name": users[item][1],
                "id":users[item][2]
            }
            
            data.append(user)
        return utils.CustomResponse.Success(data,status=200)


class UserDetail(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a user instance.
    """
    serializer_class = UserSerializer
    queryset = get_user_model().objects.all()

    def retrieve(self, request, *args, **kwargs):
        response = super().retrieve(request, *args, **kwargs)
        # get the user role if it is none then return an empry group permissions
        if response.data['groups'] == None:
            response.data['group_permissions'] = []
        else:
            # list permission inside the role 
            _group_instance = models.Group.objects.get(name=response.data['groups']['name']).permissions.all()
            # append the permissions to the response data
            response.data['group_permissions'] = PermissionSerializer(_group_instance, many=True).data
        return utils.CustomResponse.Success(response.data, response.status_code)
    
    def patch(self, request, *args, **kwargs):
        kwargs['partial'] = True

        user_id = kwargs.get('pk')

        try:
            user = get_user_model().objects.get(id=user_id)
        except get_user_model().DoesNotExist:
            return utils.CustomResponse.Failure(error="User Does Not Exist", status=404)
        
        response = super().partial_update(request, *args, **kwargs)
        # check if the user has provided 
        keys = request.data.keys()
        # check if the the Company_id sent is not an integer
        if 'Company_id' in list(keys):
            if type(request.data['Company_id']) != int:
                return utils.CustomResponse.Failure(error="Invalid Data type for Company_id", status=400)
            try:
                models.Companies.objects.get(Company_id=request.data['Company_id'])
            except models.Companies.DoesNotExist:
                return utils.CustomResponse.Failure(error="Company Does Not Exist", status=404)
            
        if 'phone_number' in list(keys):
            if type(request.data['phone_number']) != str:
                return utils.CustomResponse.Failure(error="Invalid Data type for Phone Number", status=400)
            if request.data['phone_number'] == '':
                return utils.CustomResponse.Failure(error="Phone Number cannot be null",status=400)
        # Check if emails is part of the data being sent then call the email function 
        if 'Email' in list(keys):
            if type(request.data['Email']) != str:
                return utils.CustomResponse.Failure(error="Invalid Data type for Email", status=400)
            if request.data['Email'] == '':
                return utils.CustomResponse.Failure(error="Email cannot be null",status=400)
            user_email = request.data['Email']
            
            try: 
                username = request.data['Name']
            except KeyError: 
                username = request.user
            email_template_name = 'email_change_template.html'
            template_context = {
                "username": username,
                "user_email": user_email
                }
            try:
                # AWS 
                email = loader.render_to_string(email_template_name, template_context)
                message = EmailMessage(subject="Account Updated Successfully",body=email,reply_to=[user_email],to=[user_email])
                message.content_subtype = "html"
                message.send()
            except:
                # BREEVO
                send_email(subject="Account Updated Successfully", body=email, sender_email=config('DEFAULT_FROM_EMAIL'), receiver_email=user_email, sender_name='Smartflowtech', reply_to=config('DEFAULT_FROM_EMAIL'))
        return utils.CustomResponse.Success(response.data, response.status_code)

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data, response.status_code)


class UserByCompany(generics.ListAPIView):
    serializer_class = UserSerializer
    def get_queryset(self):
        company_pk = self.kwargs.get('pk')
        queryset =  get_user_model().objects.filter(Company__pk=company_pk)
        return queryset

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        return utils.CustomResponse.Success(response.data)


class UserByCompanies(APIView):
    serializer_class = UserSerializer
    def get(self, request):
        company_ids = request.GET['company'].split(',')
        query = get_user_model().objects.filter(Company__in=company_ids)
    
        serializer = UserSerializer(query, many=True)
        return utils.CustomResponse.Success(serializer.data)

class UserActivationDetail(APIView):
    @extend_schema(responses=UserSerializer)
    def post(self, request, *args, **kwargs):
        pk = self.kwargs.get('pk')
        user = get_object_or_404(get_user_model(), pk=pk)
        if user.is_active:
            user.is_active = False
            user.save()
        else:
            user.is_active = True
            user.save()

        serializer = UserSerializer(user)
        return utils.CustomResponse.Success(serializer.data)

class UserProfileImg(APIView):
    permission_classes = ()
    authentication_classes = ()
    """
    Upload user profile image.
    """
    def get(self, request, format=None):
        qs = get_user_model().objects.all()
        user_email = request.query_params.get('email')
        user = get_object_or_404(qs, Email=user_email)
        if user.pk:
            save_profile = models.UserProfileImg.objects.filter(email = user_email).values()
            serializer = UserProfileSerializerImg(save_profile, many=True)
            return utils.CustomResponse.Success(serializer.data, status=status.HTTP_200_OK) 
        return utils.CustomResponse.Failure('Invalid email provided', status=status.HTTP_400_BAD_REQUEST) 

    def post(self, request, format=None):
        qs = get_user_model().objects.all()
        user_email = request.data['email']
        url_path = request.data['url_path']
        user = get_object_or_404(qs, Email=user_email)
        if user.pk:
            save_profile = save_users_profile(user_email,url_path,user.pk)
            return utils.CustomResponse.Success(save_profile, status=status.HTTP_200_OK) 
        return utils.CustomResponse.Failure('Invalid email provided', status=status.HTTP_400_BAD_REQUEST)