from ..companies.serializer import CompanySerializer
from ..roles.serializer import RoleSerializer
from ..sites.serializer import SiteSerializer
from django.contrib.auth import get_user_model
from rest_framework import serializers
from backend import models
from django.contrib.auth.models import Group, Permission
from ..roles.serializer import PermissionSerializer


def validate_required(value):
		if value == '' or value is None:
			raise serializers.ValidationError('This Filed is required')


class ModulesSerializer(serializers.ModelSerializer):

	class Meta:
		model = models.Modules
		fields = ('module_id', 'module_name')


class UserAccessPermissionsSerializer(serializers.ModelSerializer):

	class Meta:
		model = models.UserAccessPermissions
		fields = ('permission_name', 'read','create','update','delete','module')


class NewUserRoleSerializer(serializers.ModelSerializer):

	class Meta:
		model = models.NewUserRole
		fields = ('Role_id', 'Name','description','role_permission',)


class GroupSerializer(serializers.ModelSerializer):
      id = serializers.ReadOnlyField()
      class Meta:
            model = Group
            fields = ['id','name']



class UserSerializer(serializers.ModelSerializer):
	
	Company = CompanySerializer(read_only=True)

	groups = GroupSerializer(read_only=True)

	Role = RoleSerializer(read_only=True)
	Sites = SiteSerializer(read_only=True, many=True)
	# user_permissions = serializers.PrimaryKeyRelatedField(read_only=True, many=True)
	user_permissions = PermissionSerializer(read_only=True, many=True) 

	user_permissions_id = serializers.PrimaryKeyRelatedField(
		queryset = Permission.objects.all(),
		source = 'user_permissions',
		write_only=True,
		many=True,
		required=False
	)
	group_id = serializers.PrimaryKeyRelatedField(
		queryset=models.Group.objects.all(), 
		error_messages={
			'required': 'Group is required',
			'does_not_exist': 'Group does not exist'
		},
		source = 'groups',
		write_only = True
	)

	Company_id = serializers.PrimaryKeyRelatedField(
		queryset=models.Companies.objects.all(),
		error_messages={
			'required': 'Company is required',
			'does_not_exist': 'Company does not exist'
		},
		source='Company',
		write_only=True)
		
	Site_id = serializers.ListField(
		child=serializers.IntegerField(write_only=True),
		write_only=True,
		required=False)
	
	def create(self, validated_data):
		site_ids = validated_data.pop('Site_id', None)
		user = get_user_model().objects.create_user(**validated_data)
		if site_ids:
			sites = models.Sites.objects.filter(pk__in=site_ids)
			user.Sites.add(*sites)
			user.save()
		return user

	def update(self, instance, validated_data):
		site_ids = validated_data.pop('Site_id', None)
		new_instance = super().update(instance, validated_data)
		if site_ids:
			new_instance.Sites.clear()
			sites = models.Sites.objects.filter(pk__in=site_ids)
			new_instance.Sites.add(*sites)
			new_instance.save()
		return new_instance
		
	class Meta:
		model = get_user_model()
		exclude = ('password',)
		
class UserProfileSerializerImg(serializers.ModelSerializer):
    class Meta:
        model = models.UserProfileImg
        fields = ['url_path',]