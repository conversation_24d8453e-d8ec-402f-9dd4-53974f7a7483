# SMARTEYE DOCUMENTATION

## API DOCUMENTATION

### Main API Documentation

The complete API documentation for internal use is available at:

```
http://your-domain.com/api/v1/docs/
```

### Third-Party API Documentation (Isolated)

Isolated API documentation specifically for third-party integrations is available at:

```
http://your-domain.com/api/v1/third-party/docs/     # Swagger UI
http://your-domain.com/api/v1/third-party/redoc/    # ReDoc format
```

The third-party documentation includes only endpoints relevant to external partners and is organized by functionality (Authentication, Company, User, Sites, Tanks, etc.). For detailed information about the third-party API setup, see [THIRD_PARTY_API_DOCS.md](./THIRD_PARTY_API_DOCS.md).

### RUNNING TEST COVERAGE TO DETERMINE MISSING TEST

Run the command below to view the test report aggregate, it will show the missing reports and missed test.

```
    coverage report -m
```

### CALCULATING PV FLAG

It should be noted that the change in the tank_Volume/Tank_height value (which is the difference in the previous tank_Volume/Tank_height and current tank_Volume/Tank_height for each tank) will be checked in the cloud. PV_flag is an integer value that shows the change status of the tank_Volume/Tank_height
value as illustrated below

<li>  If tank_Volume_Old = tank_Volume_New, then PV_flag = 1  i.e. no change in volume.</li>
<li> If tank_Volume_Old > tank_Volume_New, then PV_flag = 2  i.e. volume is reducing. </li>
<li> If tank_Volume_Old < tank_Volume_New, then PV_flag = 3  i.e. volume is increasing. </li>
<hr>

### HYDROSTATIC IMPLEMENTATION

<b> R & D REQUEST </b>
The respective Hydrostatic data(remote config.) and logs formats are explained below; I hereby request for the following APIs/Endpoints:
<li> Hydrostatic(HYD)\_PIC-Config ---GET Method </li>

   <li>  HYD Tank inventory logs     ---POST Method </li>
   <li> HYD Tank inventory logs comfirmation (using its Unique_ID)  ---GET Method </li>
   <li> HYD Tank volume API (Return tank volume equivalent to the sent tank height value on the tank chart)... ---GET Method </li>

### CONFIG FORMAT

```
{"data":
    {"Tank_type":"GMC-485-H","Tank_count":1,"tank_data":
    [
        {"Tank_id":1,"polling_addr":1,"Tank_index":1,"probe_length":2}]
    },
    "errors":"",
    "code":200,
    "status":"success"}
```

### HYDROSTATIC FORMAT

```
2. HYD Tank logs format:
Keys -
[["MAC_Address",Tank_Index,Pol_Addr,"Read_Time","Tank_height","tank_Type",Tank_ID,"Unique_ID"]]


Sample log -
[["Pic-00001",1,1,"2024-05-03 18:17:09","0.059060","GMC-485-H","1","Pic-00001000101000030102"]]

```

### CONFIRMING LOGS

REQUIREMENTS: SENDING THE LOGS ID WILL CONFIRM A LOG, WITH y INDICATING A SUCCESS AND n INDICATING FAILURE

## ROLES AND PERMISSION DOCUMENTATION

roles and permissions <br>

```
    UPON FIRST SETUP Please Create a Super Admin Role on the DB also check for the user_group_detail table to update the role there accordingly, to avoid 403 error or other error like 500 server error or roles was not created,
    ensure the role is also active, when a role is not active the user will also get a 403 error.
```

#### SUPER ADMIN ROLE

a super admin has all the permissions over the system, they can create, delete, activate and decaivate all roles, they can assign roles to users, they havve access to all companies and sites, they can perform all C.R.U.D operations

#### PRODUCT ADMIN ROLE

a product admin has all the priviledge of a super admin, but they cant deactivate, delete and assign users to super admin role and they have access to all companies and sites

#### COMAPNY ADMIN ROLE

a company admin has all the priviledges to perform C.R.U.D operation on all modules subscribed to by that company and their level of action is limited to their own company, they cannot activate or deactivate a superadmin, or product admin account, they cannot assign a user to a product admin, they cannot assign a user to superadmin.

the roles of a company admin can be updated by including and excluding certain permissions to the account or the roles.

### OTHER ROLES (ENGINEER, CUSTOM ROLES e.t.c.)

Roles can be created by super admin, product admin, and company admin.

<li> permissions can be added to the roles </li> 
<li> permissions can be added to the user account directly and it will still take effect on the UI </li> 
<li> Custom permissionss can be created but this is only possible dictly on the DB to enhnace security </li>

### FLOW FOR CREATING SITES

{{BASE_URL}}/api/v1/sites/ (POST REQUEST)

request body
{
"Company_id": Integer //Required
"Name": "String", //Required
"Country": "String", //Required
"State": "String", //Required
"City": "String", //Required
"Address": "String", //Required
"Latitude": "String", //Optional
"Longitude": "String", //Optional
"Location_status": Boolean, //Optional
"Site_type": "String", //Optional
"Notes": "String", //Optional
"Device_id": Integer, //Optional
"SIM_provided_details": "String", //Required
"Number_of_tanks": Integer, // Required
"Reorder_mail": "String" //Required,
"Critical_level_mail": "String", //Required
"Contact_person_name": "String", //Required
"Contact_person_designation": "String",//Required
"Contact_person_mail": "String", //Required
"Contact_person_phone": "String", //Required
"Communication_status": Boolean, //Optional
"Email_Notification": Boolean, //Optional
"smarttank_access": Boolean, //Optional
"genhours_access": Boolean, //Optional
"smartpump_access": Boolean, //Optional
"country_id": Integer, //Required
}

### FLOW FOR CREATING COMPANY

{{BASE_URL}}/api/v1/companies/ (POST)

{
"Name": "String", //Required
"Country": "String", //Reqired
"State": "String", //Required
"City": "String", //Required
"Address": "String",
"number_of_sites": Integer, //Required
"Company_type": "String", // Optional
"Company_image": "Sting, // Optional
"Notes": "String", // Optional
"Contact_person_name": "String",//Required
"Contact_person_designation": "String", //Required
"Contact_person_mail": "String", //Required
"Contact_person_phone": "String", //Required
"genhours_access": Boolean, // Optional Defaults to False
"smarttank_access": Boolean, //Optional Defaults to False
"smartpump_access": Boolean, //Optional Defaults to False
"shift_management_access": Boolean, //Optional Defaults to False
"group": [],
"analytics_access": Boolean //Optional Defaults to False
}

### FLOW FOR CREATING PUMPS AND NOZZLES

{{BASE_URL}}/api/v1/smartpumps/pumps (POST)
{
"Name":"String", //Required
"Pump_protocol":"String"//Required,
"Nozzle_count": Integer, //Required,
"Device": "String" //Required Must be a Pump Device,
"Site": Integer, //Required
"Pumpbrand": Integer, //Required
"Company": Integer, //Required
"Nozzles":[
{
"Name":"String",
"Nozzle_address": Integer,
"Product": Integer,
"Decimal_setting_price_unit": Integer,
"Decimal_setting_amount":Integer,
"Decimal_setting_volume": Integer,
"Totalizer_at_installation": Integer,
"Display_unit": "String",
"Nominal_flow_rate": "String"
}
]
}

### PUMP VIEW ON THE UI AND THE BACKEND

When the pump model, COmpany model, Site model is modified then there is a need to modify the smartpump module which returns all pump record to the UI to Avoid Data Mismatch on the UI

Also ensure to Test the Smartpump module on the UI, from the dynamic Product dropdown.

Re-Run the Unit Test on the smartpump module
