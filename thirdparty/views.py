# from rest_framework.views import APIView
# from rest_framework.response import Response
# from rest_framework.permissions import AllowAny
# from .authentication import APIKeyAuthentication
# from .throttles import APIKeyRateThrottle
# from drf_spectacular.utils import extend_schema


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from drf_spectacular.renderers import OpenApiJsonRenderer, OpenApiYamlRenderer
from drf_spectacular.utils import extend_schema
from drf_spectacular.views import SpectacularAPIView

from thirdparty.authentication import APIKeyAuthentication
from thirdparty.throttles import APIKeyRateThrottle
from thirdparty.utils import flatten_multiple_apps
from thirdparty.schema import ThirdPartySchemaGenerator


class ThirdPartySpectacularAPIView(SpectacularAPIView):
    """
    Custom API view that generates OpenAPI schema specifically for third-party endpoints.
    This provides isolated documentation for external integrations.
    """

    def get_generator_class(self):
        """Return our custom schema generator class."""
        return ThirdPartySchemaGenerator


@extend_schema(tags=["Third Party - Example"])
class ThirdPartyExampleView(APIView):
    """
    Example endpoint for third-party API testing and demonstration.
    """
    authentication_classes = [APIKeyAuthentication]
    throttle_classes = [APIKeyRateThrottle]
    permission_classes = [AllowAny]

    @extend_schema(
        summary="Third Party Example Endpoint",
        description="A simple example endpoint for testing third-party API access and authentication",
        responses={200: {"type": "object", "properties": {
            "message": {"type": "string"}}}},
        tags=["Third Party - Example"]
    )
    def get(self, request):
        return Response({"message": "Hello third-party partner!"})
