from drf_spectacular.generators import SchemaGenerator
from drf_spectacular.views import SpectacularAPIView
import re


class ThirdPartySchemaGenerator(SchemaGenerator):
    """
    Custom schema generator for third-party APIs that filters and organizes
    endpoints specifically for third-party integrations.
    """

    def __init__(self, *args, **kwargs):
        # Set default values for third-party API documentation
        kwargs.setdefault('title', 'Third Party API Documentation')
        kwargs.setdefault(
            'description', 'API documentation for third-party integrations and external services')
        kwargs.setdefault('version', '1.0.0')
        super().__init__(*args, **kwargs)

    def get_endpoints(self, request):
        """
        Filter and organize endpoints for third-party APIs.
        Only include endpoints that are tagged as third-party or match third-party URL patterns.
        """
        all_endpoints = super().get_endpoints(request)
        third_party_endpoints = {}

        for path, (view, data) in all_endpoints.items():
            # Check if this is a third-party endpoint
            is_third_party = False

            # Check if already tagged as third-party
            if "tags" in data and data["tags"]:
                if any("third-party" in tag.lower() or "third party" in tag.lower() for tag in data["tags"]):
                    is_third_party = True

            # Check URL pattern for third-party paths
            if "/third-party/" in path or "/api/v1/third-party/" in path:
                is_third_party = True

            # Also check for smart-tanks specifically
            if "/smart-tanks/" in path:
                is_third_party = True

            if is_third_party:
                # Ensure proper tagging for organization
                if "tags" not in data or not data["tags"]:
                    # Auto-generate tags based on URL structure
                    if "/smart-tanks/" in path:
                        data["tags"] = ["Third Party - Smart Tanks"]
                    else:
                        match = re.search(r'/third-party/([^/]+)/', path)
                        if match:
                            group = match.group(1).replace(
                                '_', ' ').replace('-', ' ').title()
                            data["tags"] = [f"Third Party - {group}"]
                        else:
                            data["tags"] = ["Third Party"]
                else:
                    # Ensure all tags are prefixed with "Third Party" for consistency
                    updated_tags = []
                    for tag in data["tags"]:
                        if not tag.lower().startswith("third"):
                            updated_tags.append(f"Third Party - {tag}")
                        else:
                            updated_tags.append(tag)
                    data["tags"] = updated_tags

                third_party_endpoints[path] = (view, data)

        return third_party_endpoints


class GroupedSchemaGenerator(SchemaGenerator):
    """
    Legacy schema generator for backward compatibility.
    Use ThirdPartySchemaGenerator for new implementations.
    """

    def get_endpoints(self, request):
        all_endpoints = super().get_endpoints(request)
        grouped = {}

        for path, (view, data) in all_endpoints.items():
            if "tags" not in data or not data["tags"]:
                match = re.search(r'/third-party/([^/]+)/', path)
                if match:
                    group = match.group(1).replace(
                        '_', ' ').replace('-', ' ').title()
                    data["tags"] = [group]
                else:
                    data["tags"] = ["Third Party"]
            grouped[path] = (view, data)
        return grouped
