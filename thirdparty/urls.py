# thirdparty/urls.py

from django.urls import path, include
from drf_spectacular.views import SpectacularSwaggerView, SpectacularRedocView
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_spectacular.renderers import OpenApiJsonRender<PERSON>, OpenApiYamlRenderer
from drf_spectacular.generators import SchemaGenerator
from rest_framework.permissions import AllowAny

from . import urls as thirdparty_urls
from .views import ThirdPartyExampleView, ThirdPartySpectacularAPIView
from .schema import ThirdPartySchemaGenerator


# Custom Swagger and ReDoc views that use our custom schema generator
class ThirdPartySwaggerView(SpectacularSwaggerView):
    def get_generator_class(self):
        return ThirdPartySchemaGenerator


class ThirdPartyRedocView(SpectacularRedocView):
    def get_generator_class(self):
        return ThirdPartySchemaGenerator


# class ThirdPartySpectacularAPIView(APIView):
#     renderer_classes = [OpenApiJson<PERSON><PERSON>er, OpenApiYamlRenderer]
#     permission_classes = [AllowAny]

#     def get(self, request, *args, **kwargs):
#         patterns = [
#             path('api/v1/third-party/', include((thirdparty_urls.urlpatterns, 'thirdparty')))
#         ]
#         generator = SchemaGenerator(
#             title="Third Party API",
#             description="Public API for third-party integrations",
#             version="v1",
#             patterns=patterns
#         )
#         schema = generator.get_schema(request=request)
#         return Response(schema)


urlpatterns = [
    path('smart-tanks/', include('thirdparty.smart_tank.urls')),
    path('example/', ThirdPartyExampleView.as_view(), name='third-party-example'),
    # Isolated schema
    path('third-party-schema/', ThirdPartySpectacularAPIView.as_view(),
         name='third-party-schema'),
    path('docs/', ThirdPartySwaggerView.as_view(url_name='third-party-schema'),
         name='third-party-docs'),
    path('redoc/', ThirdPartyRedocView.as_view(url_name='third-party-schema'),
         name='third-party-redoc'),
]
